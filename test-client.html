<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 转发代理测试客户端</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .message {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .message.sent {
            color: #007bff;
        }
        .message.received {
            color: #28a745;
        }
        .message.system {
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket 转发代理测试客户端 (多目标模式)</h1>

        <div class="form-group">
            <label for="proxyUrl">代理服务器地址:</label>
            <input type="text" id="proxyUrl" value="ws://localhost:8080" placeholder="ws://localhost:8080">
        </div>

        <div class="form-group">
            <label for="targetSelect">选择目标连接:</label>
            <select id="targetSelect">
                <option value="">请先获取目标列表</option>
            </select>
            <button onclick="loadTargets()">刷新目标列表</button>
        </div>

        <div class="form-group">
            <small style="color: #666; font-size: 12px;">
                多目标模式：代理服务器可以同时维护多个目标连接，客户端可以选择连接到特定的目标
            </small>
        </div>
        
        <div class="form-group">
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
            <button id="subscribeBtn" onclick="sendSignalRSubscription()" disabled>发送 SignalR 订阅</button>
        </div>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="form-group">
            <label>控制操作:</label>
            <button id="pingBtn" onclick="sendPing()" disabled>发送 Ping</button>
            <button id="statusBtn" onclick="requestStatus()" disabled>查询状态</button>
            <button id="subscribeBtn" onclick="sendSignalRSubscription()" disabled>发送 SignalR 订阅</button>
        </div>

        <div class="form-group">
            <label for="messageInput">发送自定义消息 (仅用于测试，不会转发):</label>
            <textarea id="messageInput" rows="3" placeholder="输入测试消息..."></textarea>
            <button id="sendBtn" onclick="sendMessage()" disabled>发送测试消息</button>
        </div>
        
        <div class="form-group">
            <label>消息日志:</label>
            <div id="messages" class="messages"></div>
            <button onclick="clearMessages()">清空日志</button>
        </div>
    </div>

    <script>
        let ws = null;
        let messageCount = 0;

        function addMessage(message, type = 'system') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateStatus(connected) {
            const statusDiv = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const sendBtn = document.getElementById('sendBtn');
            const subscribeBtn = document.getElementById('subscribeBtn');

            if (connected) {
                statusDiv.textContent = '已连接';
                statusDiv.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                sendBtn.disabled = false;
                subscribeBtn.disabled = false;
            } else {
                statusDiv.textContent = '未连接';
                statusDiv.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sendBtn.disabled = true;
                subscribeBtn.disabled = true;
            }
        }

        async function loadTargets() {
            try {
                const response = await fetch('http://localhost:8080/api/targets');
                const data = await response.json();

                const select = document.getElementById('targetSelect');
                select.innerHTML = '<option value="">不选择目标（查看可用目标）</option>';

                if (data.success && data.targets.length > 0) {
                    data.targets.forEach(target => {
                        const option = document.createElement('option');
                        option.value = target.id;
                        option.textContent = `${target.name} (${target.type}) - ${target.status} - ${target.clientCount} 客户端`;
                        select.appendChild(option);
                    });
                    addMessage(`已加载 ${data.targets.length} 个目标连接`);
                } else {
                    addMessage('没有可用的目标连接');
                }
            } catch (error) {
                addMessage(`加载目标列表失败: ${error.message}`, 'system');
            }
        }

        function connect() {
            const proxyUrl = document.getElementById('proxyUrl').value;
            const targetId = document.getElementById('targetSelect').value;

            if (!proxyUrl) {
                alert('请输入代理服务器地址');
                return;
            }

            let fullUrl = proxyUrl;
            if (targetId) {
                fullUrl += `?target=${targetId}`;
                addMessage(`正在连接到代理服务器: ${proxyUrl}，目标ID: ${targetId}`);
            } else {
                addMessage(`正在连接到代理服务器: ${proxyUrl}（不指定目标）`);
            }

            try {
                ws = new WebSocket(fullUrl);

                ws.onopen = function(event) {
                    addMessage('WebSocket 连接已建立');
                    updateStatus(true);
                };

                ws.onmessage = function(event) {
                    messageCount++;
                    let messageData = event.data;

                    // 尝试解析系统消息
                    try {
                        const parsed = JSON.parse(messageData);
                        if (parsed.type === 'system') {
                            addMessage(`[系统] ${parsed.message}`, 'system');
                            return;
                        }
                    } catch (e) {
                        // 不是 JSON 格式，按普通消息处理
                    }

                    addMessage(`收到消息 #${messageCount}: ${messageData}`, 'received');
                };

                ws.onclose = function(event) {
                    addMessage(`连接已关闭 (代码: ${event.code}, 原因: ${event.reason || '无'})`);
                    updateStatus(false);
                    ws = null;
                };

                ws.onerror = function(error) {
                    addMessage(`WebSocket 错误: ${error.message || '连接失败'}`);
                    updateStatus(false);
                };

            } catch (error) {
                addMessage(`连接失败: ${error.message}`);
                updateStatus(false);
            }
        }

        function disconnect() {
            if (ws) {
                addMessage('正在断开连接...');
                ws.close();
            }
        }

        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();

            if (!message) {
                alert('请输入要发送的消息');
                return;
            }

            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(message);
                addMessage(`发送消息: ${message}`, 'sent');
                messageInput.value = '';
            } else {
                alert('WebSocket 未连接');
            }
        }

        function sendSignalRSubscription() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('WebSocket 未连接');
                return;
            }

            const signalrSubscription = {
                H: "Streaming",
                M: "Subscribe",
                A: [
                    [
                        "Heartbeat",
                        "CarData.z",
                        "Position.z",
                        "ExtrapolatedClock",
                        "TimingStats",
                        "TimingAppData",
                        "WeatherData",
                        "TrackStatus",
                        "DriverList",
                        "RaceControlMessages",
                        "SessionInfo",
                        "SessionData",
                        "LapCount",
                        "TimingData",
                        "TeamRadio",
                    ],
                ],
                I: 1,
            };

            const message = JSON.stringify(signalrSubscription);
            ws.send(message);
            addMessage(`发送 SignalR 订阅: ${message}`, 'sent');
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
            messageCount = 0;
        }

        // 允许按 Enter 键发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 页面加载时添加欢迎消息
        window.onload = function() {
            addMessage('WebSocket 转发代理测试客户端已就绪 (多目标模式)');
            addMessage('多目标模式下，代理服务器可以同时维护多个目标连接');
            addMessage('客户端可以选择连接到特定的目标服务器');
            addMessage('请先刷新目标列表，然后选择目标并连接');
            loadTargets();
        };
    </script>
</body>
</html>
