<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 转发代理测试客户端</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .message {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .message.sent {
            color: #007bff;
        }
        .message.received {
            color: #28a745;
        }
        .message.system {
            color: #6c757d;
            font-style: italic;
        }

        .message.error {
            color: #dc3545;
            font-weight: bold;
        }

        /* 新增样式 */
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }

        .section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .section h3 {
            color: #555;
            margin-bottom: 10px;
        }

        .form-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .config-section {
            margin-top: 10px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 4px;
        }

        .primary-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .primary-btn:hover {
            background-color: #0056b3;
        }

        .secondary-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .secondary-btn:hover {
            background-color: #545b62;
        }

        .api-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }

        .api-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .api-btn:hover {
            background-color: #1e7e34;
        }

        .api-response {
            border: 1px solid #ddd;
            min-height: 100px;
            max-height: 300px;
            overflow-y: auto;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }

        .targets-list {
            margin-top: 10px;
        }

        .target-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
            background-color: white;
        }

        .target-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .target-name {
            font-weight: bold;
            font-size: 16px;
        }

        .target-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .target-status.connected {
            background-color: #d4edda;
            color: #155724;
        }

        .target-status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }

        .target-info {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }

        .target-actions {
            display: flex;
            gap: 10px;
        }

        .delete-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .delete-btn:hover {
            background-color: #c82333;
        }

        .monitoring-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .monitor-card {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .monitor-card h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
        }

        .status-indicator {
            font-size: 18px;
            font-weight: bold;
            padding: 8px;
            border-radius: 4px;
        }

        .status-indicator.healthy {
            background-color: #d4edda;
            color: #155724;
        }

        .status-indicator.unhealthy {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-indicator.unknown {
            background-color: #e2e3e5;
            color: #383d41;
        }

        .metric {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }

        .json-validation {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }

        .json-validation.valid {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .json-validation.invalid {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .json-templates {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
        }

        .json-templates button {
            background-color: #17a2b8;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .json-templates button:hover {
            background-color: #138496;
        }

        #jsonInput {
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-left: 10px;
            font-size: 14px;
            cursor: pointer;
        }

        .checkbox-label input[type="checkbox"] {
            margin: 0;
        }

        .form-row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket 转发代理测试客户端 & API 调试工具</h1>

        <!-- API 配置区域 -->
        <div class="section">
            <h2>🔧 API 配置</h2>
            <div class="form-group">
                <label for="apiBaseUrl">API 基础地址:</label>
                <input type="text" id="apiBaseUrl" value="http://localhost:8080" placeholder="http://localhost:8080">
            </div>
            <div class="form-group">
                <label for="apiKey">API 密钥 (可选):</label>
                <input type="password" id="apiKey" placeholder="输入 API 密钥">
                <small style="color: #666;">如果服务器启用了 API 密钥认证，请输入密钥</small>
            </div>
        </div>

        <!-- 目标连接管理区域 -->
        <div class="section">
            <h2>🎯 目标连接管理</h2>

            <!-- 创建新目标连接 -->
            <div class="form-group">
                <h3>创建新目标连接</h3>
                <div class="form-row">
                    <input type="text" id="targetName" placeholder="连接名称" style="flex: 1;">
                    <select id="targetType" style="flex: 1;">
                        <option value="websocket">WebSocket</option>
                        <option value="signalr">SignalR</option>
                    </select>
                </div>

                <!-- WebSocket 配置 -->
                <div id="websocketConfig" class="config-section">
                    <input type="text" id="websocketUrl" placeholder="ws://echo.websocket.org" style="width: 100%;">
                </div>

                <!-- SignalR 配置 -->
                <div id="signalrConfig" class="config-section" style="display: none;">
                    <input type="text" id="signalrUrl" placeholder="livetiming.formula1.com/signalr" style="width: 100%; margin-bottom: 5px;">
                    <div class="form-row">
                        <input type="text" id="signalrHub" placeholder="Hub 名称" value="Streaming" style="flex: 1;">
                        <input type="text" id="signalrProtocol" placeholder="协议版本" value="1.5" style="flex: 1;">
                    </div>
                </div>

                <button onclick="createTarget()" class="primary-btn">创建目标连接</button>
            </div>

            <!-- 目标连接列表 -->
            <div class="form-group">
                <h3>现有目标连接</h3>
                <button onclick="loadTargets()" class="secondary-btn">刷新目标列表</button>
                <div id="targetsList" class="targets-list"></div>
            </div>
        </div>

        <!-- WebSocket 客户端测试区域 -->
        <div class="section">
            <h2>📡 WebSocket 客户端测试</h2>

            <div class="form-group">
                <label for="proxyUrl">代理服务器地址:</label>
                <input type="text" id="proxyUrl" value="ws://localhost:8080" placeholder="ws://localhost:8080">
            </div>

            <div class="form-group">
                <label for="targetSelect">选择目标连接:</label>
                <select id="targetSelect">
                    <option value="">不指定目标（查看可用目标）</option>
                </select>
            </div>

            <div class="form-group">
                <small style="color: #666; font-size: 12px;">
                    多目标模式：代理服务器可以同时维护多个目标连接，客户端可以选择连接到特定的目标
                </small>
            </div>
        
        <div class="form-group">
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
            <button id="subscribeBtn" onclick="sendSignalRSubscription()" disabled>发送 SignalR 订阅</button>
        </div>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div class="form-group">
            <label>控制操作:</label>
            <button id="pingBtn" onclick="sendPing()" disabled>发送 Ping</button>
            <button id="statusBtn" onclick="requestStatus()" disabled>查询状态</button>
            <button id="subscribeBtn" onclick="sendSignalRSubscription()" disabled>发送 SignalR 订阅</button>
        </div>

        <div class="form-group">
            <label for="messageInput">发送自定义消息:</label>
            <textarea id="messageInput" rows="3" placeholder="输入测试消息..."></textarea>
            <div class="form-row">
                <button id="sendBtn" onclick="sendMessage()" disabled>发送测试消息</button>
                <label class="checkbox-label">
                    <input type="checkbox" id="autoForwardMode" onchange="toggleAutoForwardMode()">
                    自动转发模式
                </label>
            </div>
            <small id="messageHint" style="color: #666;">
                默认模式：消息不会自动转发到目标服务器。启用自动转发模式后，所有消息都会自动转发。
            </small>
        </div>

        <div class="form-group">
            <label for="forwardInput">转发文本消息到目标服务器:</label>
            <textarea id="forwardInput" rows="3" placeholder="输入要转发到目标服务器的文本消息..."></textarea>
            <button id="forwardBtn" onclick="forwardMessage()" disabled>转发文本消息</button>
            <small style="color: #666;">转发文本消息到你选择的目标服务器</small>
        </div>

        <div class="form-group">
            <label for="jsonInput">转发 JSON 数据到目标服务器:</label>
            <textarea id="jsonInput" rows="6" placeholder='输入要转发的 JSON 数据，例如:
{
  "command": "getData",
  "params": {
    "id": 123,
    "type": "user"
  }
}'></textarea>
            <div class="form-row">
                <button id="forwardJsonBtn" onclick="forwardJsonData()" disabled>转发 JSON 数据</button>
                <button onclick="formatJson()">格式化 JSON</button>
                <button onclick="validateJson()">验证 JSON</button>
            </div>
            <div id="jsonValidation" class="json-validation"></div>
            <small style="color: #666;">转发结构化 JSON 数据到目标服务器</small>
        </div>

        <div class="form-group">
            <label>快速 JSON 模板:</label>
            <div class="json-templates">
                <button onclick="loadJsonTemplate('command')">命令模板</button>
                <button onclick="loadJsonTemplate('signalr')">SignalR 模板</button>
                <button onclick="loadJsonTemplate('query')">查询模板</button>
                <button onclick="loadJsonTemplate('custom')">自定义模板</button>
            </div>
        </div>

        <div class="form-group">
            <button id="signalrSubBtn" onclick="triggerSignalRSubscribe()" disabled>触发 SignalR 订阅</button>
            <small style="color: #666;">手动触发 SignalR 订阅（仅适用于 SignalR 目标）</small>
        </div>
        
        </div>

        <!-- API 调试区域 -->
        <div class="section">
            <h2>🔍 API 调试工具</h2>

            <div class="api-buttons">
                <button onclick="getServerInfo()" class="api-btn">获取服务器信息</button>
                <button onclick="getServerStatus()" class="api-btn">获取连接状态</button>
                <button onclick="getServerHealth()" class="api-btn">健康检查</button>
                <button onclick="getServerConfig()" class="api-btn">获取配置</button>
            </div>

            <div class="form-group">
                <h3>API 响应</h3>
                <div id="apiResponse" class="api-response"></div>
            </div>
        </div>

        <!-- 服务器监控区域 -->
        <div class="section">
            <h2>📊 服务器监控</h2>

            <div class="form-group">
                <button onclick="startMonitoring()" id="monitorBtn" class="secondary-btn">开始监控</button>
                <button onclick="stopMonitoring()" class="secondary-btn">停止监控</button>
                <span id="monitorStatus" style="margin-left: 10px; color: #666;">监控已停止</span>
            </div>

            <div class="monitoring-grid">
                <div class="monitor-card">
                    <h4>服务器状态</h4>
                    <div id="serverStatus" class="status-indicator">未知</div>
                </div>
                <div class="monitor-card">
                    <h4>客户端连接数</h4>
                    <div id="clientCount" class="metric">-</div>
                </div>
                <div class="monitor-card">
                    <h4>目标连接数</h4>
                    <div id="targetCount" class="metric">-</div>
                </div>
                <div class="monitor-card">
                    <h4>服务器运行时间</h4>
                    <div id="serverUptime" class="metric">-</div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label>消息日志:</label>
            <div id="messages" class="messages"></div>
            <button onclick="clearMessages()">清空日志</button>
        </div>
    </div>

    <script>
        let ws = null;
        let messageCount = 0;

        function addMessage(message, type = 'system') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateStatus(connected) {
            const statusDiv = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const sendBtn = document.getElementById('sendBtn');
            const subscribeBtn = document.getElementById('subscribeBtn');
            const forwardBtn = document.getElementById('forwardBtn');
            const forwardJsonBtn = document.getElementById('forwardJsonBtn');
            const signalrSubBtn = document.getElementById('signalrSubBtn');

            if (connected) {
                statusDiv.textContent = '已连接';
                statusDiv.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                sendBtn.disabled = false;
                subscribeBtn.disabled = false;
                if (forwardBtn) forwardBtn.disabled = false;
                if (forwardJsonBtn) forwardJsonBtn.disabled = false;
                if (signalrSubBtn) signalrSubBtn.disabled = false;
            } else {
                statusDiv.textContent = '未连接';
                statusDiv.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sendBtn.disabled = true;
                subscribeBtn.disabled = true;
                if (forwardBtn) forwardBtn.disabled = true;
                if (forwardJsonBtn) forwardJsonBtn.disabled = true;
                if (signalrSubBtn) signalrSubBtn.disabled = true;
            }
        }

        // API 配置
        function getApiConfig() {
            const baseUrl = document.getElementById('apiBaseUrl').value;
            const apiKey = document.getElementById('apiKey').value;

            const headers = {
                'Content-Type': 'application/json'
            };

            if (apiKey) {
                headers['X-API-Key'] = apiKey;
            }

            return { baseUrl, headers };
        }

        // 显示 API 响应
        function displayApiResponse(data, error = null) {
            const responseDiv = document.getElementById('apiResponse');
            if (error) {
                responseDiv.textContent = `错误: ${error}`;
                responseDiv.style.color = '#dc3545';
            } else {
                responseDiv.textContent = JSON.stringify(data, null, 2);
                responseDiv.style.color = '#333';
            }
        }

        // 目标连接类型切换
        document.getElementById('targetType').addEventListener('change', function() {
            const type = this.value;
            const websocketConfig = document.getElementById('websocketConfig');
            const signalrConfig = document.getElementById('signalrConfig');

            if (type === 'websocket') {
                websocketConfig.style.display = 'block';
                signalrConfig.style.display = 'none';
            } else {
                websocketConfig.style.display = 'none';
                signalrConfig.style.display = 'block';
            }
        });

        // 创建目标连接
        async function createTarget() {
            const { baseUrl, headers } = getApiConfig();
            const name = document.getElementById('targetName').value;
            const type = document.getElementById('targetType').value;

            if (!name) {
                alert('请输入连接名称');
                return;
            }

            let payload = { name };

            if (type === 'websocket') {
                const url = document.getElementById('websocketUrl').value;
                if (!url) {
                    alert('请输入 WebSocket URL');
                    return;
                }
                payload.url = url;
            } else {
                const url = document.getElementById('signalrUrl').value;
                const hub = document.getElementById('signalrHub').value;
                const protocol = document.getElementById('signalrProtocol').value;

                if (!url || !hub || !protocol) {
                    alert('请填写完整的 SignalR 配置');
                    return;
                }

                payload.signalr = { url, hub, protocol };
            }

            try {
                const response = await fetch(`${baseUrl}/api/targets`, {
                    method: 'POST',
                    headers,
                    body: JSON.stringify(payload)
                });

                const data = await response.json();
                displayApiResponse(data);

                if (data.success) {
                    addMessage(`目标连接创建成功: ${data.connection.name}`, 'system');
                    loadTargets(); // 刷新目标列表

                    // 清空表单
                    document.getElementById('targetName').value = '';
                    document.getElementById('websocketUrl').value = '';
                    document.getElementById('signalrUrl').value = '';
                } else {
                    addMessage(`创建失败: ${data.error}`, 'system');
                }
            } catch (error) {
                displayApiResponse(null, error.message);
                addMessage(`创建目标连接失败: ${error.message}`, 'system');
            }
        }

        async function loadTargets() {
            const { baseUrl } = getApiConfig();

            try {
                const response = await fetch(`${baseUrl}/api/targets`);
                const data = await response.json();

                // 更新下拉选择框
                const select = document.getElementById('targetSelect');
                select.innerHTML = '<option value="">不选择目标（查看可用目标）</option>';

                // 更新目标列表显示
                const targetsList = document.getElementById('targetsList');
                targetsList.innerHTML = '';

                if (data.success && data.targets.length > 0) {
                    data.targets.forEach(target => {
                        // 添加到下拉选择框
                        const option = document.createElement('option');
                        option.value = target.id;
                        option.textContent = `${target.name} (${target.type}) - ${target.status} - ${target.clientCount} 客户端`;
                        select.appendChild(option);

                        // 添加到目标列表
                        const targetItem = createTargetItem(target);
                        targetsList.appendChild(targetItem);
                    });
                    addMessage(`已加载 ${data.targets.length} 个目标连接`, 'system');
                } else {
                    targetsList.innerHTML = '<p style="color: #666;">没有可用的目标连接</p>';
                    addMessage('没有可用的目标连接', 'system');
                }
            } catch (error) {
                displayApiResponse(null, error.message);
                addMessage(`加载目标列表失败: ${error.message}`, 'system');
            }
        }

        // 创建目标连接项目元素
        function createTargetItem(target) {
            const div = document.createElement('div');
            div.className = 'target-item';

            const uptime = target.uptime ? Math.floor(target.uptime / 1000) + 's' : '-';
            const hasInit = target.hasInitMessage !== null ? (target.hasInitMessage ? '是' : '否') : '-';

            div.innerHTML = `
                <div class="target-header">
                    <span class="target-name">${target.name}</span>
                    <span class="target-status ${target.status}">${target.status}</span>
                </div>
                <div class="target-info">
                    <strong>ID:</strong> ${target.id} |
                    <strong>类型:</strong> ${target.type} |
                    <strong>客户端:</strong> ${target.clientCount} |
                    <strong>消息数:</strong> ${target.messagesReceived} |
                    <strong>运行时间:</strong> ${uptime}
                    ${target.type === 'signalr' ? ` | <strong>初始化消息:</strong> ${hasInit}` : ''}
                </div>
                <div class="target-info">
                    <strong>URL:</strong> ${target.url}
                </div>
                <div class="target-actions">
                    <button onclick="deleteTarget(${target.id})" class="delete-btn">删除连接</button>
                </div>
            `;

            return div;
        }

        // 删除目标连接
        async function deleteTarget(targetId) {
            if (!confirm('确定要删除这个目标连接吗？')) {
                return;
            }

            const { baseUrl, headers } = getApiConfig();

            try {
                const response = await fetch(`${baseUrl}/api/targets/${targetId}`, {
                    method: 'DELETE',
                    headers
                });

                const data = await response.json();
                displayApiResponse(data);

                if (data.success) {
                    addMessage(`目标连接 #${targetId} 已删除`, 'system');
                    loadTargets(); // 刷新目标列表
                } else {
                    addMessage(`删除失败: ${data.error}`, 'system');
                }
            } catch (error) {
                displayApiResponse(null, error.message);
                addMessage(`删除目标连接失败: ${error.message}`, 'system');
            }
        }

        function connect() {
            const proxyUrl = document.getElementById('proxyUrl').value;
            const targetId = document.getElementById('targetSelect').value;

            if (!proxyUrl) {
                alert('请输入代理服务器地址');
                return;
            }

            let fullUrl = proxyUrl;
            if (targetId) {
                fullUrl += `?target=${targetId}`;
                addMessage(`正在连接到代理服务器: ${proxyUrl}，目标ID: ${targetId}`);
            } else {
                addMessage(`正在连接到代理服务器: ${proxyUrl}（不指定目标）`);
            }

            try {
                ws = new WebSocket(fullUrl);

                ws.onopen = function(event) {
                    addMessage('WebSocket 连接已建立');
                    updateStatus(true);
                };

                ws.onmessage = function(event) {
                    messageCount++;
                    let messageData = event.data;

                    // 尝试解析 JSON 消息
                    try {
                        const parsed = JSON.parse(messageData);

                        if (parsed.type === 'system') {
                            addMessage(`[系统] ${parsed.message}`, 'system');
                            return;
                        }

                        if (parsed.type === 'forward_response') {
                            const status = parsed.success ? '✅' : '❌';
                            addMessage(`${status} 转发响应: ${parsed.message}`, parsed.success ? 'system' : 'error');
                            if (parsed.originalMessage) {
                                addMessage(`   原始消息: ${parsed.originalMessage}`, 'system');
                            }
                            return;
                        }

                        if (parsed.type === 'auto_forward_response') {
                            const status = parsed.success ? '✅' : '❌';
                            addMessage(`${status} 自动转发响应: ${parsed.message}`, parsed.success ? 'system' : 'error');
                            if (parsed.originalMessage) {
                                addMessage(`   原始消息: ${parsed.originalMessage}`, 'system');
                            }
                            return;
                        }

                        if (parsed.type === 'signalr_subscribe_response') {
                            const status = parsed.success ? '✅' : '❌';
                            addMessage(`${status} SignalR 订阅响应: ${parsed.message}`, parsed.success ? 'system' : 'error');
                            return;
                        }

                        if (parsed.type === 'status_response') {
                            addMessage(`[状态] 模式: ${parsed.mode}, 目标: ${parsed.totalTargets}, 客户端: ${parsed.totalClients}`, 'system');
                            return;
                        }

                        if (parsed.type === 'error') {
                            addMessage(`[错误] ${parsed.message}`, 'error');
                            return;
                        }

                        // 其他 JSON 消息
                        addMessage(`[JSON] ${parsed.type || 'unknown'}: ${JSON.stringify(parsed).substring(0, 100)}...`, 'received');

                    } catch (e) {
                        // 不是 JSON 格式，按普通消息处理
                        addMessage(`收到消息 #${messageCount}: ${messageData}`, 'received');
                    }
                };

                ws.onclose = function(event) {
                    addMessage(`连接已关闭 (代码: ${event.code}, 原因: ${event.reason || '无'})`);
                    updateStatus(false);
                    ws = null;
                };

                ws.onerror = function(error) {
                    addMessage(`WebSocket 错误: ${error.message || '连接失败'}`);
                    updateStatus(false);
                };

            } catch (error) {
                addMessage(`连接失败: ${error.message}`);
                updateStatus(false);
            }
        }

        function disconnect() {
            if (ws) {
                addMessage('正在断开连接...');
                ws.close();
            }
        }

        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            const autoForwardMode = document.getElementById('autoForwardMode').checked;

            if (!message) {
                alert('请输入要发送的消息');
                return;
            }

            if (ws && ws.readyState === WebSocket.OPEN) {
                if (autoForwardMode) {
                    // 自动转发模式：直接发送原始消息
                    ws.send(message);
                    addMessage(`发送消息 (自动转发模式): ${message}`, 'sent');
                } else {
                    // 默认模式：发送测试消息，不转发
                    ws.send(message);
                    addMessage(`发送测试消息 (不转发): ${message}`, 'sent');
                }
                messageInput.value = '';
            } else {
                alert('WebSocket 未连接');
            }
        }

        function forwardMessage() {
            const forwardInput = document.getElementById('forwardInput');
            const message = forwardInput.value.trim();

            if (!message) {
                alert('请输入要转发的消息');
                return;
            }

            if (ws && ws.readyState === WebSocket.OPEN) {
                // 发送转发消息到目标服务器
                const forwardData = {
                    type: 'forward_to_target',
                    data: message
                };

                ws.send(JSON.stringify(forwardData));
                addMessage(`转发文本消息到目标服务器: ${message}`, 'sent');
                forwardInput.value = '';
            } else {
                alert('WebSocket 未连接');
            }
        }

        function forwardJsonData() {
            const jsonInput = document.getElementById('jsonInput');
            const jsonText = jsonInput.value.trim();

            if (!jsonText) {
                alert('请输入要转发的 JSON 数据');
                return;
            }

            // 验证 JSON 格式
            let jsonData;
            try {
                jsonData = JSON.parse(jsonText);
            } catch (error) {
                alert('JSON 格式错误: ' + error.message);
                return;
            }

            if (ws && ws.readyState === WebSocket.OPEN) {
                // 发送转发 JSON 数据到目标服务器
                const forwardData = {
                    type: 'forward_to_target',
                    data: jsonData
                };

                ws.send(JSON.stringify(forwardData));
                addMessage(`转发 JSON 数据到目标服务器: ${JSON.stringify(jsonData)}`, 'sent');
                jsonInput.value = '';
                clearJsonValidation();
            } else {
                alert('WebSocket 未连接');
            }
        }

        function validateJson() {
            const jsonInput = document.getElementById('jsonInput');
            const jsonValidation = document.getElementById('jsonValidation');
            const jsonText = jsonInput.value.trim();

            if (!jsonText) {
                jsonValidation.textContent = '';
                jsonValidation.className = 'json-validation';
                return;
            }

            try {
                const parsed = JSON.parse(jsonText);
                jsonValidation.textContent = '✅ JSON 格式有效';
                jsonValidation.className = 'json-validation valid';
            } catch (error) {
                jsonValidation.textContent = '❌ JSON 格式错误: ' + error.message;
                jsonValidation.className = 'json-validation invalid';
            }
        }

        function formatJson() {
            const jsonInput = document.getElementById('jsonInput');
            const jsonText = jsonInput.value.trim();

            if (!jsonText) {
                alert('请先输入 JSON 数据');
                return;
            }

            try {
                const parsed = JSON.parse(jsonText);
                const formatted = JSON.stringify(parsed, null, 2);
                jsonInput.value = formatted;
                validateJson();
            } catch (error) {
                alert('JSON 格式错误，无法格式化: ' + error.message);
            }
        }

        function clearJsonValidation() {
            const jsonValidation = document.getElementById('jsonValidation');
            jsonValidation.textContent = '';
            jsonValidation.className = 'json-validation';
        }

        function loadJsonTemplate(type) {
            const jsonInput = document.getElementById('jsonInput');
            let template = '';

            switch (type) {
                case 'command':
                    template = JSON.stringify({
                        command: "getData",
                        params: {
                            id: 123,
                            type: "user"
                        }
                    }, null, 2);
                    break;

                case 'signalr':
                    template = JSON.stringify({
                        H: "MyHub",
                        M: "SendMessage",
                        A: ["Hello SignalR!", "Additional parameter"]
                    }, null, 2);
                    break;

                case 'query':
                    template = JSON.stringify({
                        action: "query",
                        table: "users",
                        filters: {
                            status: "active",
                            created_after: "2024-01-01"
                        },
                        limit: 10
                    }, null, 2);
                    break;

                case 'custom':
                    template = JSON.stringify({
                        type: "custom_message",
                        timestamp: new Date().toISOString(),
                        data: {
                            message: "Your custom data here",
                            priority: "high"
                        }
                    }, null, 2);
                    break;

                default:
                    template = '{\n  "key": "value"\n}';
            }

            jsonInput.value = template;
            validateJson();
        }

        function triggerSignalRSubscribe() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const subscribeData = {
                    type: 'signalr_subscribe'
                };

                ws.send(JSON.stringify(subscribeData));
                addMessage('触发 SignalR 订阅', 'sent');
            } else {
                alert('WebSocket 未连接');
            }
        }

        function toggleAutoForwardMode() {
            const checkbox = document.getElementById('autoForwardMode');
            const hint = document.getElementById('messageHint');

            if (checkbox.checked) {
                hint.textContent = '自动转发模式：所有发送的消息都会自动转发到目标服务器。';
                hint.style.color = '#28a745';
                addMessage('[模式] 已启用自动转发模式', 'system');
            } else {
                hint.textContent = '默认模式：消息不会自动转发到目标服务器。使用 "转发到目标服务器" 功能来手动转发。';
                hint.style.color = '#666';
                addMessage('[模式] 已禁用自动转发模式', 'system');
            }
        }

        function sendSignalRSubscription() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('WebSocket 未连接');
                return;
            }

            const signalrSubscription = {
                H: "Streaming",
                M: "Subscribe",
                A: [
                    [
                        "Heartbeat",
                        "CarData.z",
                        "Position.z",
                        "ExtrapolatedClock",
                        "TimingStats",
                        "TimingAppData",
                        "WeatherData",
                        "TrackStatus",
                        "DriverList",
                        "RaceControlMessages",
                        "SessionInfo",
                        "SessionData",
                        "LapCount",
                        "TimingData",
                        "TeamRadio",
                    ],
                ],
                I: 1,
            };

            const message = JSON.stringify(signalrSubscription);
            ws.send(message);
            addMessage(`发送 SignalR 订阅: ${message}`, 'sent');
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
            messageCount = 0;
        }

        // 允许按 Enter 键发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // API 调试功能
        async function getServerInfo() {
            const { baseUrl } = getApiConfig();
            try {
                const response = await fetch(`${baseUrl}/`);
                const data = await response.json();
                displayApiResponse(data);
                addMessage('已获取服务器信息', 'system');
            } catch (error) {
                displayApiResponse(null, error.message);
                addMessage(`获取服务器信息失败: ${error.message}`, 'system');
            }
        }

        async function getServerStatus() {
            const { baseUrl } = getApiConfig();
            try {
                const response = await fetch(`${baseUrl}/status`);
                const data = await response.json();
                displayApiResponse(data);
                addMessage('已获取服务器状态', 'system');
            } catch (error) {
                displayApiResponse(null, error.message);
                addMessage(`获取服务器状态失败: ${error.message}`, 'system');
            }
        }

        async function getServerHealth() {
            const { baseUrl } = getApiConfig();
            try {
                const response = await fetch(`${baseUrl}/health`);
                const data = await response.json();
                displayApiResponse(data);
                addMessage(`健康检查: ${data.status}`, 'system');
            } catch (error) {
                displayApiResponse(null, error.message);
                addMessage(`健康检查失败: ${error.message}`, 'system');
            }
        }

        async function getServerConfig() {
            const { baseUrl } = getApiConfig();
            try {
                const response = await fetch(`${baseUrl}/api/config`);
                const data = await response.json();
                displayApiResponse(data);
                addMessage('已获取服务器配置', 'system');
            } catch (error) {
                displayApiResponse(null, error.message);
                addMessage(`获取服务器配置失败: ${error.message}`, 'system');
            }
        }

        // 监控功能
        let monitoringInterval = null;

        async function startMonitoring() {
            if (monitoringInterval) {
                return;
            }

            document.getElementById('monitorBtn').disabled = true;
            document.getElementById('monitorStatus').textContent = '监控中...';
            document.getElementById('monitorStatus').style.color = '#28a745';

            // 立即执行一次
            await updateMonitoringData();

            // 每5秒更新一次
            monitoringInterval = setInterval(updateMonitoringData, 5000);
            addMessage('开始服务器监控', 'system');
        }

        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
            }

            document.getElementById('monitorBtn').disabled = false;
            document.getElementById('monitorStatus').textContent = '监控已停止';
            document.getElementById('monitorStatus').style.color = '#666';
            addMessage('停止服务器监控', 'system');
        }

        async function updateMonitoringData() {
            const { baseUrl } = getApiConfig();

            try {
                // 获取健康状态
                const healthResponse = await fetch(`${baseUrl}/health`);
                const healthData = await healthResponse.json();

                const serverStatusEl = document.getElementById('serverStatus');
                serverStatusEl.textContent = healthData.status;
                serverStatusEl.className = `status-indicator ${healthData.status === 'healthy' ? 'healthy' : 'unhealthy'}`;

                // 获取详细状态
                const statusResponse = await fetch(`${baseUrl}/status`);
                const statusData = await statusResponse.json();

                document.getElementById('clientCount').textContent = statusData.totalClients || 0;

                // 获取目标连接数
                const targetsResponse = await fetch(`${baseUrl}/api/targets`);
                const targetsData = await targetsResponse.json();
                document.getElementById('targetCount').textContent = targetsData.targets ? targetsData.targets.length : 0;

                // 获取服务器信息
                const infoResponse = await fetch(`${baseUrl}/`);
                const infoData = await infoResponse.json();

                if (infoData.uptime) {
                    const uptimeSeconds = Math.floor(infoData.uptime / 1000);
                    const hours = Math.floor(uptimeSeconds / 3600);
                    const minutes = Math.floor((uptimeSeconds % 3600) / 60);
                    const seconds = uptimeSeconds % 60;
                    document.getElementById('serverUptime').textContent = `${hours}h ${minutes}m ${seconds}s`;
                } else {
                    document.getElementById('serverUptime').textContent = '-';
                }

            } catch (error) {
                console.error('监控数据更新失败:', error);

                const serverStatusEl = document.getElementById('serverStatus');
                serverStatusEl.textContent = '连接失败';
                serverStatusEl.className = 'status-indicator unknown';
            }
        }

        // 页面加载时添加欢迎消息
        window.onload = function() {
            addMessage('WebSocket 转发代理测试客户端 & API 调试工具已就绪');
            addMessage('功能包括：WebSocket 连接测试、目标连接管理、API 调试、服务器监控、JSON 数据转发');
            addMessage('请先配置 API 设置，然后刷新目标列表');
            loadTargets();

            // 添加 JSON 输入框的实时验证
            const jsonInput = document.getElementById('jsonInput');
            if (jsonInput) {
                jsonInput.addEventListener('input', function() {
                    // 延迟验证，避免频繁触发
                    clearTimeout(this.validationTimeout);
                    this.validationTimeout = setTimeout(validateJson, 500);
                });
            }
        };
    </script>
</body>
</html>
