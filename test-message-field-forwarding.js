const WebSocket = require('ws');

console.log('🎯 测试基于消息字段的转发控制\n');

// 简单的 HTTP 请求函数
function httpRequest(url, method = 'GET', data = null) {
  const http = require('http');
  const urlParts = new URL(url);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function createTestTarget() {
  console.log('🔗 创建测试目标连接 (Echo Server)...');
  
  try {
    const response = await httpRequest('http://localhost:8080/api/targets', 'POST', {
      name: 'Message Field Test Echo',
      url: 'ws://echo.websocket.org'
    });
    
    if (response.status === 200 && response.data.success) {
      console.log(`✅ 目标连接创建成功，ID: ${response.data.connection.id}`);
      return response.data.connection.id;
    } else {
      console.log('❌ 目标连接创建失败:', response.data.error || response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ 创建目标连接时出错:', error.message);
    return null;
  }
}

async function testMessageFieldForwarding(targetId) {
  console.log(`\n🧪 测试基于消息字段的转发控制 (目标 ID: ${targetId})...\n`);
  
  return new Promise((resolve) => {
    const wsUrl = `ws://localhost:8080?target=${targetId}`;
    const ws = new WebSocket(wsUrl);
    
    let testResults = {
      connected: false,
      forwardTrueTest: false,
      forwardFalseTest: false,
      noForwardFieldTest: false,
      forwardWithDataTest: false,
      forwardWithoutDataTest: false,
      echoReceived: 0,
      systemMessages: 0
    };
    
    // 测试消息
    const testMessages = [
      {
        name: 'forward: true 带 data 字段',
        message: {
          forward: true,
          data: {
            command: "getData",
            params: { id: 123 }
          }
        },
        shouldForward: true,
        expectedForwardContent: '{"command":"getData","params":{"id":123}}'
      },
      {
        name: 'forward: true 不带 data 字段',
        message: {
          forward: true,
          command: "processData",
          value: 456
        },
        shouldForward: true,
        expectedForwardContent: '{"command":"processData","value":456}'
      },
      {
        name: 'forward: false',
        message: {
          forward: false,
          data: "This should not be forwarded"
        },
        shouldForward: false
      },
      {
        name: '没有 forward 字段',
        message: {
          command: "normalMessage",
          data: "This should not be forwarded"
        },
        shouldForward: false
      },
      {
        name: 'Phoenix Channel 消息带 forward: true',
        message: {
          forward: true,
          data: ["6", "6", "driver_radio_transcriptions:session:9921", "phx_join", {}]
        },
        shouldForward: true,
        expectedForwardContent: '["6","6","driver_radio_transcriptions:session:9921","phx_join",{}]'
      }
    ];
    
    let testIndex = 0;
    
    const timeout = setTimeout(() => {
      console.log('⏰ 测试超时');
      ws.close();
      resolve(testResults);
    }, 30000);
    
    ws.on('open', () => {
      console.log('✅ WebSocket 连接已建立');
      testResults.connected = true;
      
      function sendNextTest() {
        if (testIndex >= testMessages.length) {
          console.log('\n✅ 所有测试消息已发送完毕');
          setTimeout(() => ws.close(), 3000);
          return;
        }
        
        const test = testMessages[testIndex];
        console.log(`\n${testIndex + 1}. 测试: ${test.name}`);
        console.log(`   发送消息: ${JSON.stringify(test.message)}`);
        console.log(`   预期转发: ${test.shouldForward ? '是' : '否'}`);
        
        if (test.expectedForwardContent) {
          console.log(`   预期转发内容: ${test.expectedForwardContent}`);
        }
        
        ws.send(JSON.stringify(test.message));
        testIndex++;
        
        // 间隔发送下一个测试
        setTimeout(sendNextTest, 4000);
      }
      
      // 等待连接稳定后开始测试
      setTimeout(sendNextTest, 2000);
    });
    
    ws.on('message', (data) => {
      const message = data.toString();
      console.log(`📥 收到响应: ${message.substring(0, 200)}...`);
      
      try {
        const parsed = JSON.parse(message);
        
        switch (parsed.type) {
          case 'system':
            console.log(`   [系统] ${parsed.message}`);
            testResults.systemMessages++;
            
            if (parsed.reason && parsed.reason.includes('未包含转发指令')) {
              console.log(`   ✅ 正确识别未包含转发指令的消息`);
              testResults.noForwardFieldTest = true;
            }
            break;
            
          case 'auto_forward_response':
            console.log(`   [转发响应] ${parsed.message}`);
            if (parsed.success) {
              console.log(`   ✅ 消息已转发到目标服务器`);
              console.log(`   📤 转发内容: ${parsed.originalMessage}`);
              
              // 根据转发内容判断测试类型
              if (parsed.originalMessage.includes('getData')) {
                testResults.forwardWithDataTest = true;
              } else if (parsed.originalMessage.includes('processData')) {
                testResults.forwardWithoutDataTest = true;
              } else if (parsed.originalMessage.includes('phx_join')) {
                testResults.forwardTrueTest = true;
              }
            }
            break;
            
          default:
            // 检查是否是来自 Echo 服务器的回显
            console.log(`   📥 收到 Echo 服务器回显`);
            testResults.echoReceived++;
            
            // 验证回显内容是否是纯净的
            if (message.includes('forward')) {
              console.log(`   ❌ 警告: 回显包含 forward 字段，应该只包含纯净数据`);
            } else {
              console.log(`   ✅ 确认: 回显是纯净数据，不包含 forward 字段`);
            }
            
            // 检查特定的测试内容
            if (message.includes('getData')) {
              console.log(`   ✅ 确认收到 getData 命令的回显`);
            } else if (message.includes('processData')) {
              console.log(`   ✅ 确认收到 processData 命令的回显`);
            } else if (message.includes('phx_join')) {
              console.log(`   ✅ 确认收到 Phoenix Channel 消息的回显`);
            }
        }
      } catch (e) {
        // 原始消息处理
        console.log(`   📥 收到原始回显: ${message.substring(0, 100)}...`);
        testResults.echoReceived++;
      }
    });
    
    ws.on('close', () => {
      clearTimeout(timeout);
      console.log('\n❌ WebSocket 连接已关闭');
      resolve(testResults);
    });
    
    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.error('💥 WebSocket 错误:', error.message);
      resolve(testResults);
    });
  });
}

async function cleanupTarget(targetId) {
  console.log(`\n🧹 清理测试目标连接 #${targetId}...`);
  
  try {
    const response = await httpRequest(`http://localhost:8080/api/targets/${targetId}`, 'DELETE');
    
    if (response.status === 200) {
      console.log('✅ 目标连接已删除');
    } else {
      console.log('⚠️  删除目标连接时出现问题');
    }
  } catch (error) {
    console.log('❌ 删除目标连接时出错:', error.message);
  }
}

async function main() {
  console.log('🎯 基于消息字段的转发控制测试套件\n');
  console.log('📋 此测试将验证：');
  console.log('   - forward: true 的消息会被转发');
  console.log('   - forward: false 的消息不会被转发');
  console.log('   - 没有 forward 字段的消息不会被转发');
  console.log('   - 转发时只发送纯净数据（移除 forward 字段）');
  console.log('   - 支持 data 字段和直接内容两种方式\n');
  
  try {
    // 1. 创建测试目标
    const targetId = await createTestTarget();
    if (!targetId) {
      console.log('❌ 无法创建测试目标，退出测试');
      return;
    }
    
    // 2. 等待目标连接建立
    console.log('⏳ 等待目标连接建立...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 3. 测试基于消息字段的转发控制
    const results = await testMessageFieldForwarding(targetId);
    
    // 4. 分析测试结果
    console.log('\n📊 基于消息字段的转发控制测试结果:');
    console.log(`   连接建立: ${results.connected ? '✅' : '❌'}`);
    console.log(`   forward: true 带 data 测试: ${results.forwardWithDataTest ? '✅' : '❌'}`);
    console.log(`   forward: true 不带 data 测试: ${results.forwardWithoutDataTest ? '✅' : '❌'}`);
    console.log(`   Phoenix Channel 转发测试: ${results.forwardTrueTest ? '✅' : '❌'}`);
    console.log(`   无 forward 字段测试: ${results.noForwardFieldTest ? '✅' : '❌'}`);
    console.log(`   Echo 回显次数: ${results.echoReceived}`);
    console.log(`   系统消息次数: ${results.systemMessages}`);
    
    const successCount = Object.values(results).filter(v => typeof v === 'boolean' && v).length;
    const totalTests = Object.keys(results).filter(k => typeof results[k] === 'boolean').length;
    
    console.log(`\n🎯 测试通过率: ${successCount}/${totalTests} (${Math.round(successCount/totalTests*100)}%)`);
    
    if (results.connected && results.forwardWithDataTest && results.noForwardFieldTest) {
      console.log('\n🎉 基于消息字段的转发控制功能测试成功！');
      console.log('   - forward: true 的消息正确转发');
      console.log('   - 没有 forward 字段的消息正确拒绝');
      console.log('   - 转发的数据是纯净的（移除了 forward 字段）');
      console.log('   - 支持多种消息格式和数据类型');
    } else {
      console.log('\n⚠️  基于消息字段的转发控制功能可能存在问题');
    }
    
    // 5. 清理测试目标
    await cleanupTarget(targetId);
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出测试...');
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  console.log('请确保代理服务器正在运行: npm run start:no-auto\n');
  main();
}
