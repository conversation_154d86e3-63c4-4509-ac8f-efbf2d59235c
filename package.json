{"name": "rewebsocket-node", "version": "1.0.0", "description": "A WebSocket forwarding proxy server built with Node.js", "main": "server.js", "scripts": {"start": "node server.js", "start:no-auto": "AUTO_CONNECT=false node server.js", "dev": "node --watch server.js", "test": "node test-client.js", "test:signalr": "node test-signalr.js", "test:signalr-init": "node test-signalr-init.js", "test:api": "node test-api-management.js", "test:api-fix": "node test-api-fix.js", "test:shutdown": "node test-shutdown.js", "test:forward": "node test-message-forwarding.js", "test:json": "node test-json-forwarding.js", "test:pure": "node test-pure-data-forwarding.js", "test:field": "node test-message-field-forwarding.js", "test:clean": "node test-clean-config.js", "test:forward-debug": "node test-forward-debug.js", "test:signalr-fix": "node test-signalr-forward-fix.js", "test:phoenix": "node test-phoenix-heartbeat.js", "verify:phoenix": "node verify-phoenix-heartbeat-format.js", "demo:phoenix": "node demo-phoenix-heartbeat.js", "test:debug": "node test-debug-logging.js", "test:auto": "node test-auto-forward.js", "test:quick": "node quick-test-auto-forward.js", "test:config": "node test-config-check.js", "test:web-api": "node test-web-auto-forward.js", "test:web-interface": "node test-web-interface.js", "test:multi": "node test-multi-targets.js", "test:web": "echo '请在浏览器中访问 http://localhost:8080 进行 Web 界面测试'", "verify": "node verify-all-fixes.js", "demo": "node demo-multiple-clients.js", "demo:f1": "node demo-f1-data.js", "start:signalr": "SIGNALR_MODE=true SIGNALR_URL=livetiming.formula1.com/signalr SIGNALR_HUB=Streaming node server.js"}, "keywords": ["websocket", "proxy", "forwarding", "nodejs"], "author": "", "license": "MIT", "dependencies": {"ws": "^8.14.2", "express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}}