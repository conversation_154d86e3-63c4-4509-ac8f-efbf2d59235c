const WebSocket = require('ws');

console.log('🔄 测试心跳循环触发修复\n');

// 简单的 HTTP 请求函数
function httpRequest(url, method = 'GET', data = null) {
  const http = require('http');
  const urlParts = new URL(url);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// 创建模拟 Phoenix 服务器，会对心跳进行回复
function createMockPhoenixServerWithHeartbeatReply() {
  return new Promise((resolve) => {
    const mockServer = new WebSocket.Server({ port: 0 });
    
    mockServer.on('connection', (ws) => {
      console.log('📡 模拟 Phoenix 服务器连接建立');
      
      let heartbeatCount = 0;
      let joinReplyCount = 0;
      
      ws.on('message', (data) => {
        const message = data.toString();
        
        try {
          const parsed = JSON.parse(message);
          
          if (Array.isArray(parsed) && parsed.length >= 4) {
            const [ref, counter, protocol, event, payload] = parsed;
            
            if (event === "phx_join") {
              joinReplyCount++;
              console.log(`🔥 收到 phx_join 消息 #${joinReplyCount}`);
              
              // 发送 phx_reply 消息，状态为 ok（这应该触发心跳）
              const replyMessage = [
                "6", "6", "driver_radio_transcriptions:session:9921", "phx_reply",
                { "status": "ok", "response": {} }
              ];
              
              setTimeout(() => {
                ws.send(JSON.stringify(replyMessage));
                console.log(`📤 发送 phx_join 回复: ${JSON.stringify(replyMessage)}`);
              }, 500);
              
            } else if (event === "heartbeat") {
              heartbeatCount++;
              console.log(`💓 收到心跳 #${heartbeatCount}: [${ref}, "${counter}", "${protocol}", "${event}", {}]`);
              
              // 模拟 Phoenix 服务器对心跳的回复（这不应该触发新的心跳）
              const heartbeatReply = [
                null, counter, "phoenix", "phx_reply",
                { "status": "ok", "response": {} }
              ];
              
              setTimeout(() => {
                ws.send(JSON.stringify(heartbeatReply));
                console.log(`📤 发送心跳回复: ${JSON.stringify(heartbeatReply)}`);
              }, 100);
              
              // 如果收到太多心跳，说明可能有循环问题
              if (heartbeatCount > 10) {
                console.log('⚠️  收到过多心跳，可能存在循环触发问题');
                ws.close();
              }
            }
          }
        } catch (e) {
          console.log(`   ⚠️ 无法解析消息: ${e.message}`);
        }
      });
      
      ws.on('close', () => {
        console.log(`\n📡 模拟 Phoenix 服务器连接关闭`);
        console.log(`📊 统计信息:`);
        console.log(`   - phx_join 消息: ${joinReplyCount}`);
        console.log(`   - 心跳消息: ${heartbeatCount}`);
        
        if (heartbeatCount <= 5 && heartbeatCount > 0) {
          console.log(`✅ 心跳数量正常，没有循环触发问题`);
        } else if (heartbeatCount > 10) {
          console.log(`❌ 心跳数量过多，可能存在循环触发`);
        } else if (heartbeatCount === 0) {
          console.log(`⚠️  没有收到心跳，心跳可能未启动`);
        }
      });
    });
    
    const port = mockServer.address().port;
    console.log(`🚀 模拟 Phoenix 服务器启动在端口 ${port}`);
    console.log(`📋 服务器行为:`);
    console.log(`   - 对 phx_join 回复 phx_reply 状态 ok`);
    console.log(`   - 对 heartbeat 回复 phx_reply 状态 ok`);
    console.log(`   - 监控是否出现循环触发\n`);
    
    resolve({
      server: mockServer,
      port: port,
      url: `ws://localhost:${port}`
    });
  });
}

async function testHeartbeatLoopFix() {
  console.log('🧪 测试心跳循环触发修复...\n');
  
  let mockServer = null;
  
  try {
    // 1. 创建模拟 Phoenix 服务器
    console.log('1. 创建模拟 Phoenix 服务器（会回复心跳）...');
    const mock = await createMockPhoenixServerWithHeartbeatReply();
    mockServer = mock.server;
    
    // 2. 在代理服务器中创建目标连接
    console.log('\n2. 创建目标连接到模拟服务器...');
    const targetResponse = await httpRequest('http://localhost:8080/api/targets', 'POST', {
      name: 'Heartbeat Loop Fix Test',
      url: mock.url
    });
    
    if (!targetResponse.data.success) {
      console.log('❌ 创建目标连接失败:', targetResponse.data.error);
      return;
    }
    
    const targetId = targetResponse.data.connection.id;
    console.log(`✅ 目标连接创建成功，ID: ${targetId}`);
    
    // 3. 等待连接建立
    console.log('\n3. 等待连接建立...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 4. 连接到代理服务器并发送 phx_join 消息
    console.log('\n4. 连接到代理服务器并发送 phx_join 消息...');
    
    const proxyWs = new WebSocket(`ws://localhost:8080?target=${targetId}`);
    
    await new Promise((resolve) => {
      const timeout = setTimeout(() => {
        console.log('\n⏰ 测试超时');
        proxyWs.close();
        resolve();
      }, 15000);
      
      proxyWs.on('open', () => {
        console.log('✅ 连接到代理服务器成功');
        
        // 发送 phx_join 消息
        const joinMessage = {
          forward: true,
          data: ["6", "6", "driver_radio_transcriptions:session:9921", "phx_join", {}]
        };
        
        console.log('📤 发送 phx_join 消息...');
        proxyWs.send(JSON.stringify(joinMessage));
      });
      
      proxyWs.on('message', (data) => {
        const message = data.toString();
        
        try {
          const parsed = JSON.parse(message);
          
          if (parsed.type === 'auto_forward_response' && parsed.success) {
            console.log('✅ phx_join 消息转发成功');
          }
        } catch (e) {
          // 忽略其他消息
        }
      });
      
      proxyWs.on('close', () => {
        clearTimeout(timeout);
        console.log('\n❌ 代理服务器连接关闭');
        resolve();
      });
      
      proxyWs.on('error', (error) => {
        clearTimeout(timeout);
        console.error('💥 代理服务器连接错误:', error.message);
        resolve();
      });
    });
    
    // 5. 等待一段时间观察心跳行为
    console.log('\n5. 等待观察心跳行为（检查是否有循环触发）...');
    await new Promise(resolve => setTimeout(resolve, 8000));
    
    // 6. 清理目标连接
    console.log('\n6. 清理目标连接...');
    await httpRequest(`http://localhost:8080/api/targets/${targetId}`, 'DELETE');
    console.log('✅ 目标连接已删除');
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
  } finally {
    // 关闭模拟服务器
    if (mockServer) {
      console.log('\n🧹 关闭模拟服务器...');
      mockServer.close();
    }
  }
}

async function main() {
  console.log('🎯 心跳循环触发修复测试套件\n');
  console.log('📋 此测试将验证：');
  console.log('   - phx_join 的回复能正确触发心跳');
  console.log('   - 心跳的回复不会重复触发心跳');
  console.log('   - 避免无限循环的心跳启动');
  console.log('   - 心跳数量保持在合理范围内\n');
  
  console.log('🔍 预期行为:');
  console.log('   1. 发送 phx_join 消息');
  console.log('   2. 收到 phx_reply 状态 ok，启动心跳');
  console.log('   3. 发送心跳消息');
  console.log('   4. 收到心跳的 phx_reply，但不重复启动心跳');
  console.log('   5. 心跳持续发送，但不会循环重启\n');
  
  await testHeartbeatLoopFix();
  
  console.log('\n📊 测试总结:');
  console.log('   - 检查服务器日志中的心跳启动信息');
  console.log('   - 确认没有重复的"启动心跳机制"消息');
  console.log('   - 验证心跳数量在合理范围内');
  console.log('   - 确认修复了循环触发问题');
  
  console.log('\n🎉 心跳循环触发修复测试完成！');
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出测试...');
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  console.log('请确保代理服务器正在运行: npm run start:no-auto\n');
  main();
}
