# WebSocket 连接管理 API 指南

## 概述

WebSocket 转发代理服务器现在支持通过 REST API 动态管理目标服务器连接，无需重启服务器即可切换不同的 WebSocket 或 SignalR 目标。

## 🚀 快速开始

### 启动服务器（不自动连接）
```bash
npm run start:no-auto
```

### 启动服务器（自动连接模式）
```bash
npm start
```

## 📡 API 端点

### 1. 连接到目标服务器
**POST** `/api/connect`

#### WebSocket 连接
```bash
curl -X POST http://localhost:8080/api/connect \
  -H "Content-Type: application/json" \
  -d '{"url": "ws://echo.websocket.org"}'
```

#### SignalR 连接
```bash
curl -X POST http://localhost:8080/api/connect \
  -H "Content-Type: application/json" \
  -d '{
    "signalr": {
      "url": "livetiming.formula1.com/signalr",
      "hub": "Streaming", 
      "protocol": "1.5"
    }
  }'
```

**响应示例:**
```json
{
  "success": true,
  "message": "连接成功",
  "connection": {
    "type": "SignalR",
    "url": "wss://livetiming.formula1.com/signalr/connect?...",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

### 2. 断开连接
**POST** `/api/disconnect`

```bash
curl -X POST http://localhost:8080/api/disconnect
```

**响应示例:**
```json
{
  "success": true,
  "message": "已断开连接",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 3. 获取配置
**GET** `/api/config`

```bash
curl http://localhost:8080/api/config
```

**响应示例:**
```json
{
  "current": {
    "targetUrl": "ws://echo.websocket.org",
    "signalrMode": false,
    "autoReconnect": true
  },
  "connection": {
    "status": "connected",
    "reconnectAttempts": 0
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 4. 更新配置
**PUT** `/api/config`

```bash
curl -X PUT http://localhost:8080/api/config \
  -H "Content-Type: application/json" \
  -d '{"autoReconnect": false}'
```

### 5. SignalR 订阅（仅 SignalR 模式）
**POST** `/signalr/subscribe`

```bash
curl -X POST http://localhost:8080/signalr/subscribe
```

## 🔄 使用流程

### 场景 1: 连接到普通 WebSocket
```bash
# 1. 启动服务器（不自动连接）
npm run start:no-auto

# 2. 连接到 WebSocket 服务器
curl -X POST http://localhost:8080/api/connect \
  -H "Content-Type: application/json" \
  -d '{"url": "ws://echo.websocket.org"}'

# 3. 检查状态
curl http://localhost:8080/status

# 4. 客户端现在可以连接并接收广播数据
```

### 场景 2: 连接到 F1 SignalR
```bash
# 1. 启动服务器
npm run start:no-auto

# 2. 连接到 F1 SignalR
curl -X POST http://localhost:8080/api/connect \
  -H "Content-Type: application/json" \
  -d '{
    "signalr": {
      "url": "livetiming.formula1.com/signalr",
      "hub": "Streaming",
      "protocol": "1.5"
    }
  }'

# 3. 等待连接建立，然后触发订阅
curl -X POST http://localhost:8080/signalr/subscribe

# 4. 客户端现在可以接收 F1 实时数据
```

### 场景 3: 动态切换目标
```bash
# 1. 连接到第一个目标
curl -X POST http://localhost:8080/api/connect \
  -d '{"url": "ws://echo.websocket.org"}'

# 2. 切换到另一个目标（会自动断开前一个）
curl -X POST http://localhost:8080/api/connect \
  -d '{"url": "ws://another-server.com"}'

# 3. 切换到 SignalR
curl -X POST http://localhost:8080/api/connect \
  -d '{
    "signalr": {
      "url": "example.com/signalr",
      "hub": "ChatHub",
      "protocol": "1.5"
    }
  }'
```

## 🧪 测试

### 自动化 API 测试
```bash
npm run test:api
```

### 手动测试步骤
1. 启动服务器: `npm run start:no-auto`
2. 打开浏览器访问: `http://localhost:8080`
3. 使用 curl 或 Postman 调用 API
4. 打开 `test-client.html` 测试客户端连接

## 📊 监控

### 服务器状态
```bash
curl http://localhost:8080/status
```

### 健康检查
```bash
curl http://localhost:8080/health
```

### 实时日志
服务器会输出详细的连接和消息日志，包括：
- API 调用记录
- 连接状态变化
- 消息广播统计
- 错误和重连信息

## ⚠️ 注意事项

1. **连接替换**: 调用 `/api/connect` 会自动断开现有连接
2. **自动重连**: 可通过 `/api/config` 控制是否启用自动重连
3. **SignalR 订阅**: SignalR 连接建立后会自动发送订阅，也可手动触发
4. **客户端通知**: 连接状态变化会通过系统消息通知所有客户端
5. **配置持久化**: 动态配置在服务器重启后会重置为默认值

## 🔧 环境变量

- `AUTO_CONNECT=false` - 禁用启动时自动连接
- `SIGNALR_MODE=true` - 启用 SignalR 模式（仅在自动连接时有效）
- `TARGET_WS_URL=ws://...` - 默认目标 URL（仅在自动连接时有效）

## 🎯 使用场景

1. **开发调试**: 在不同的测试环境间快速切换
2. **生产运维**: 无停机切换数据源
3. **多环境部署**: 同一个代理服务器连接不同的后端服务
4. **故障转移**: 主服务器故障时快速切换到备用服务器
5. **A/B 测试**: 在不同的数据源间进行对比测试
