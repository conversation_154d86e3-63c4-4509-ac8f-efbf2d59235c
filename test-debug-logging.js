const WebSocket = require('ws');

// 配置
const API_BASE = 'http://localhost:8080';
const WS_BASE = 'ws://localhost:8080';

console.log('🐛 测试调试日志功能\n');

// 简单的 HTTP 请求函数
function httpRequest(url, method = 'GET', data = null) {
  const http = require('http');
  const urlParts = new URL(url);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function createTestTarget() {
  console.log('🔗 创建测试目标连接 (Echo Server)...');
  
  try {
    const response = await httpRequest(`${API_BASE}/api/targets`, 'POST', {
      name: 'Debug Test Echo Server',
      url: 'ws://echo.websocket.org'
    });
    
    if (response.status === 200 && response.data.success) {
      console.log(`✅ 目标连接创建成功，ID: ${response.data.connection.id}`);
      return response.data.connection.id;
    } else {
      console.log('❌ 目标连接创建失败:', response.data.error || response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ 创建目标连接时出错:', error.message);
    return null;
  }
}

async function testDebugLogging(targetId) {
  console.log(`\n🐛 测试调试日志功能 (目标 ID: ${targetId})...\n`);
  console.log('📝 注意：请查看服务器控制台输出以验证调试日志\n');
  
  return new Promise((resolve) => {
    const wsUrl = `${WS_BASE}?target=${targetId}`;
    const ws = new WebSocket(wsUrl);
    
    let testStep = 0;
    const testData = [
      {
        name: '文本消息转发',
        data: 'Hello Debug Test!'
      },
      {
        name: '简单 JSON 转发',
        data: {
          message: "Debug JSON test",
          timestamp: new Date().toISOString()
        }
      },
      {
        name: '复杂 JSON 转发',
        data: {
          command: "debug_test",
          params: {
            test_id: 12345,
            test_type: "logging",
            nested_data: {
              level: "info",
              category: "forwarding",
              details: {
                source: "test-debug-logging.js",
                target: "echo.websocket.org"
              }
            }
          },
          metadata: {
            version: "1.0",
            created_at: new Date().toISOString()
          }
        }
      },
      {
        name: '特殊字符 JSON 转发',
        data: {
          special_chars: "测试中文字符 & Special chars: !@#$%^&*()",
          unicode: "🎯🔧📨💡",
          escaped_json: "{\"nested\": \"value\"}",
          array_data: [1, 2, 3, "test", true, null]
        }
      }
    ];
    
    const timeout = setTimeout(() => {
      console.log('⏰ 测试超时');
      ws.close();
      resolve();
    }, 30000);
    
    ws.on('open', () => {
      console.log('✅ WebSocket 连接已建立');
      console.log('🔍 开始发送测试数据，请观察服务器日志...\n');
      
      function sendNextTest() {
        if (testStep >= testData.length) {
          console.log('\n✅ 所有测试数据已发送完毕');
          console.log('📋 请检查服务器控制台是否显示了以下调试信息：');
          console.log('   1. 📨 客户端请求转发消息的详细信息');
          console.log('   2. 🔄 转发消息到目标服务器的详细信息');
          console.log('   3. 📥 目标服务器收到消息的详细信息');
          console.log('   4. 📤 广播消息给客户端的信息');
          console.log('   5. JSON 数据的美化显示');
          
          setTimeout(() => {
            ws.close();
          }, 3000);
          return;
        }
        
        const test = testData[testStep];
        console.log(`${testStep + 1}. 发送 ${test.name}:`);
        
        if (typeof test.data === 'object') {
          console.log(`   JSON 数据: ${JSON.stringify(test.data, null, 2)}`);
        } else {
          console.log(`   文本数据: ${test.data}`);
        }
        
        ws.send(JSON.stringify({
          type: 'forward_to_target',
          data: test.data
        }));
        
        testStep++;
        
        // 间隔发送下一个测试
        setTimeout(sendNextTest, 3000);
      }
      
      // 等待一秒后开始测试
      setTimeout(sendNextTest, 1000);
    });
    
    ws.on('message', (data) => {
      const message = data.toString();
      
      try {
        const parsed = JSON.parse(message);
        
        if (parsed.type === 'forward_response') {
          const status = parsed.success ? '✅' : '❌';
          console.log(`   ${status} 转发响应: ${parsed.message}`);
        } else if (parsed.type === 'system') {
          console.log(`   [系统] ${parsed.message}`);
        } else {
          // 可能是来自 Echo 服务器的回显
          console.log(`   📥 收到回显: ${message.substring(0, 100)}...`);
        }
      } catch (e) {
        // 原始消息
        console.log(`   📥 收到原始回显: ${message.substring(0, 100)}...`);
      }
    });
    
    ws.on('close', () => {
      clearTimeout(timeout);
      console.log('\n❌ WebSocket 连接已关闭');
      resolve();
    });
    
    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.error('💥 WebSocket 错误:', error.message);
      resolve();
    });
  });
}

async function cleanupTarget(targetId) {
  console.log(`\n🧹 清理测试目标连接 #${targetId}...`);
  
  try {
    const response = await httpRequest(`${API_BASE}/api/targets/${targetId}`, 'DELETE');
    
    if (response.status === 200) {
      console.log('✅ 目标连接已删除');
    } else {
      console.log('⚠️  删除目标连接时出现问题');
    }
  } catch (error) {
    console.log('❌ 删除目标连接时出错:', error.message);
  }
}

async function main() {
  console.log('🎯 调试日志功能测试套件\n');
  console.log('📋 此测试将验证以下调试功能：');
  console.log('   - 客户端转发请求的详细日志');
  console.log('   - 转发到目标服务器的详细日志');
  console.log('   - 目标服务器响应的详细日志');
  console.log('   - JSON 数据的美化显示');
  console.log('   - 消息大小和类型信息\n');
  
  try {
    // 1. 创建测试目标
    const targetId = await createTestTarget();
    if (!targetId) {
      console.log('❌ 无法创建测试目标，退出测试');
      return;
    }
    
    // 2. 等待目标连接建立
    console.log('⏳ 等待目标连接建立...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 3. 测试调试日志
    await testDebugLogging(targetId);
    
    // 4. 清理测试目标
    await cleanupTarget(targetId);
    
    console.log('\n🎉 调试日志测试完成！');
    console.log('💡 提示：如果服务器控制台显示了详细的调试信息，说明功能正常工作');
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出测试...');
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  console.log('请确保代理服务器正在运行: npm run start:no-auto\n');
  main();
}
