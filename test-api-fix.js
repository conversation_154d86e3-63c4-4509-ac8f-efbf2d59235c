// 使用内置的 fetch (Node.js 18+) 或 http 模块
const http = require('http');

// 简单的 HTTP GET 请求函数
function httpGet(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(data)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: data
          });
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });
  });
}

// 测试 API 修复
async function testAPIs() {
  const baseUrl = 'http://localhost:8080';

  console.log('🔧 测试 API 修复...\n');

  try {
    // 测试主页
    console.log('1. 测试主页 API...');
    const homeResponse = await httpGet(`${baseUrl}/`);
    console.log(`   状态: ${homeResponse.status}`);
    console.log(`   版本: ${homeResponse.data.version}`);
    console.log(`   模式: ${homeResponse.data.name}`);

    // 测试健康检查
    console.log('\n2. 测试健康检查 API...');
    const healthResponse = await httpGet(`${baseUrl}/health`);
    console.log(`   状态: ${healthResponse.status}`);
    console.log(`   健康状态: ${healthResponse.data.status}`);

    // 测试目标列表
    console.log('\n3. 测试目标列表 API...');
    const targetsResponse = await httpGet(`${baseUrl}/api/targets`);
    console.log(`   状态: ${targetsResponse.status}`);
    console.log(`   目标数量: ${targetsResponse.data.targets.length}`);

    // 测试配置 API
    console.log('\n4. 测试配置 API...');
    const configResponse = await httpGet(`${baseUrl}/api/config`);
    console.log(`   状态: ${configResponse.status}`);
    console.log(`   模式: ${configResponse.data.mode}`);

    // 测试状态 API
    console.log('\n5. 测试状态 API...');
    const statusResponse = await httpGet(`${baseUrl}/status`);
    console.log(`   状态: ${statusResponse.status}`);
    console.log(`   客户端数: ${statusResponse.data.totalClients}`);

    console.log('\n✅ 所有 API 测试通过！');

  } catch (error) {
    console.error('❌ API 测试失败:', error.message);
    console.log('\n请确保服务器正在运行: npm run start:no-auto');
  }
}

if (require.main === module) {
  testAPIs();
}
