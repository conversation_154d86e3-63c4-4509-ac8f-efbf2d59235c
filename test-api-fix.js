const fetch = require('node-fetch');

// 测试 API 修复
async function testAPIs() {
  const baseUrl = 'http://localhost:8080';
  
  console.log('🔧 测试 API 修复...\n');
  
  try {
    // 测试主页
    console.log('1. 测试主页 API...');
    const homeResponse = await fetch(`${baseUrl}/`);
    const homeData = await homeResponse.json();
    console.log(`   状态: ${homeResponse.status}`);
    console.log(`   版本: ${homeData.version}`);
    console.log(`   模式: ${homeData.name}`);
    
    // 测试健康检查
    console.log('\n2. 测试健康检查 API...');
    const healthResponse = await fetch(`${baseUrl}/health`);
    const healthData = await healthResponse.json();
    console.log(`   状态: ${healthResponse.status}`);
    console.log(`   健康状态: ${healthData.status}`);
    
    // 测试目标列表
    console.log('\n3. 测试目标列表 API...');
    const targetsResponse = await fetch(`${baseUrl}/api/targets`);
    const targetsData = await targetsResponse.json();
    console.log(`   状态: ${targetsResponse.status}`);
    console.log(`   目标数量: ${targetsData.targets.length}`);
    
    // 测试配置 API
    console.log('\n4. 测试配置 API...');
    const configResponse = await fetch(`${baseUrl}/api/config`);
    const configData = await configResponse.json();
    console.log(`   状态: ${configResponse.status}`);
    console.log(`   模式: ${configData.mode}`);
    
    // 测试状态 API
    console.log('\n5. 测试状态 API...');
    const statusResponse = await fetch(`${baseUrl}/status`);
    const statusData = await statusResponse.json();
    console.log(`   状态: ${statusResponse.status}`);
    console.log(`   客户端数: ${statusData.totalClients}`);
    
    console.log('\n✅ 所有 API 测试通过！');
    
  } catch (error) {
    console.error('❌ API 测试失败:', error.message);
    console.log('\n请确保服务器正在运行: npm run start:no-auto');
  }
}

if (require.main === module) {
  testAPIs();
}
