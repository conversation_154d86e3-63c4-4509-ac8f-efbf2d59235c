const WebSocket = require('ws');
const { spawn } = require('child_process');

// 配置
const API_BASE = 'http://localhost:8080';
const WS_BASE = 'ws://localhost:8080';

console.log('🔄 测试自动转发功能\n');

// 简单的 HTTP 请求函数
function httpRequest(url, method = 'GET', data = null) {
  const http = require('http');
  const urlParts = new URL(url);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function createTestTarget() {
  console.log('🔗 创建测试目标连接 (Echo Server)...');
  
  try {
    const response = await httpRequest(`${API_BASE}/api/targets`, 'POST', {
      name: 'Auto Forward Test Echo Server',
      url: 'ws://echo.websocket.org'
    });
    
    if (response.status === 200 && response.data.success) {
      console.log(`✅ 目标连接创建成功，ID: ${response.data.connection.id}`);
      return response.data.connection.id;
    } else {
      console.log('❌ 目标连接创建失败:', response.data.error || response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ 创建目标连接时出错:', error.message);
    return null;
  }
}

async function testAutoForwardMode(targetId) {
  console.log(`\n🔄 测试自动转发模式 (目标 ID: ${targetId})...\n`);
  
  return new Promise((resolve) => {
    const wsUrl = `${WS_BASE}?target=${targetId}`;
    const ws = new WebSocket(wsUrl);
    
    let testResults = {
      connected: false,
      defaultModeTest: false,
      autoForwardTest: false,
      jsonArrayTest: false,
      phoenixChannelTest: false
    };
    
    const testMessages = [
      {
        name: '默认模式测试',
        message: 'Default mode test message',
        expectAutoForward: false
      },
      {
        name: 'JSON 数组测试',
        message: '["6","6","driver_radio_transcriptions:session:9921","phx_join",{}]',
        expectAutoForward: false
      },
      {
        name: 'Phoenix Channel 测试',
        message: '["1","1","room:lobby","phx_join",{"user_id":"123"}]',
        expectAutoForward: false
      }
    ];
    
    let testIndex = 0;
    
    const timeout = setTimeout(() => {
      console.log('⏰ 测试超时');
      ws.close();
      resolve(testResults);
    }, 20000);
    
    ws.on('open', () => {
      console.log('✅ WebSocket 连接已建立');
      testResults.connected = true;
      
      function runNextTest() {
        if (testIndex >= testMessages.length) {
          console.log('\n✅ 所有测试完成');
          setTimeout(() => ws.close(), 1000);
          return;
        }
        
        const test = testMessages[testIndex];
        console.log(`${testIndex + 1}. ${test.name}:`);
        console.log(`   发送消息: ${test.message}`);
        
        ws.send(test.message);
        testIndex++;
        
        // 等待响应后继续下一个测试
        setTimeout(runNextTest, 3000);
      }
      
      // 等待连接稳定后开始测试
      setTimeout(runNextTest, 1000);
    });
    
    ws.on('message', (data) => {
      const message = data.toString();
      console.log(`📥 收到响应: ${message.substring(0, 150)}...`);
      
      try {
        const parsed = JSON.parse(message);
        
        switch (parsed.type) {
          case 'system':
            console.log(`   [系统] ${parsed.message}`);
            if (parsed.message.includes('不转发客户端消息')) {
              console.log('   ✅ 默认模式正常工作 - 消息未自动转发');
              testResults.defaultModeTest = true;
            }
            if (parsed.hint) {
              console.log(`   💡 提示: ${parsed.hint.substring(0, 100)}...`);
            }
            break;
            
          case 'auto_forward_response':
            console.log(`   ✅ 自动转发响应: ${parsed.message}`);
            testResults.autoForwardTest = true;
            break;
            
          default:
            // 可能是来自 Echo 服务器的回显
            if (message.includes('phx_join') || message.includes('driver_radio_transcriptions')) {
              console.log('   ✅ 收到 Echo 服务器回显 - 消息已转发');
              if (message.includes('driver_radio_transcriptions')) {
                testResults.jsonArrayTest = true;
              } else if (message.includes('room:lobby')) {
                testResults.phoenixChannelTest = true;
              }
            }
        }
      } catch (e) {
        // 原始消息处理
        if (message.includes('phx_join') || message.includes('driver_radio_transcriptions')) {
          console.log('   ✅ 收到原始回显消息');
        }
      }
    });
    
    ws.on('close', () => {
      clearTimeout(timeout);
      console.log('❌ WebSocket 连接已关闭');
      resolve(testResults);
    });
    
    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.error('💥 WebSocket 错误:', error.message);
      resolve(testResults);
    });
  });
}

async function testWithAutoForwardEnabled(targetId) {
  console.log(`\n🚀 测试启用自动转发模式...\n`);
  
  // 启动带有自动转发的服务器实例
  console.log('启动带有自动转发的服务器实例...');
  const serverProcess = spawn('node', ['server.js'], {
    env: { 
      ...process.env, 
      AUTO_CONNECT: 'false',
      AUTO_FORWARD: 'true',
      PORT: '8081'
    },
    stdio: ['pipe', 'pipe', 'pipe']
  });
  
  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  return new Promise((resolve) => {
    const wsUrl = `ws://localhost:8081?target=${targetId}`;
    const ws = new WebSocket(wsUrl);
    
    let autoForwardResults = {
      connected: false,
      autoForwardWorking: false
    };
    
    const timeout = setTimeout(() => {
      serverProcess.kill();
      resolve(autoForwardResults);
    }, 10000);
    
    ws.on('open', () => {
      console.log('✅ 连接到自动转发服务器');
      autoForwardResults.connected = true;
      
      // 发送测试消息
      ws.send('["test","auto","forward","message",{}]');
    });
    
    ws.on('message', (data) => {
      const message = data.toString();
      console.log(`📥 自动转发响应: ${message.substring(0, 100)}...`);
      
      try {
        const parsed = JSON.parse(message);
        if (parsed.type === 'auto_forward_response' && parsed.success) {
          console.log('✅ 自动转发功能正常工作');
          autoForwardResults.autoForwardWorking = true;
        }
      } catch (e) {
        // 可能是回显消息
        if (message.includes('auto') && message.includes('forward')) {
          console.log('✅ 收到自动转发的回显消息');
          autoForwardResults.autoForwardWorking = true;
        }
      }
    });
    
    ws.on('close', () => {
      clearTimeout(timeout);
      serverProcess.kill();
      resolve(autoForwardResults);
    });
    
    ws.on('error', () => {
      clearTimeout(timeout);
      serverProcess.kill();
      resolve(autoForwardResults);
    });
  });
}

async function cleanupTarget(targetId) {
  console.log(`\n🧹 清理测试目标连接 #${targetId}...`);
  
  try {
    const response = await httpRequest(`${API_BASE}/api/targets/${targetId}`, 'DELETE');
    
    if (response.status === 200) {
      console.log('✅ 目标连接已删除');
    } else {
      console.log('⚠️  删除目标连接时出现问题');
    }
  } catch (error) {
    console.log('❌ 删除目标连接时出错:', error.message);
  }
}

async function main() {
  console.log('🎯 自动转发功能测试套件\n');
  console.log('📋 此测试将验证：');
  console.log('   - 默认模式下消息不自动转发');
  console.log('   - 自动转发模式下消息自动转发');
  console.log('   - JSON 数组消息的处理');
  console.log('   - Phoenix Channel 消息的处理\n');
  
  try {
    // 1. 创建测试目标
    const targetId = await createTestTarget();
    if (!targetId) {
      console.log('❌ 无法创建测试目标，退出测试');
      return;
    }
    
    // 2. 等待目标连接建立
    console.log('⏳ 等待目标连接建立...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 3. 测试默认模式
    const defaultResults = await testAutoForwardMode(targetId);
    
    // 4. 测试自动转发模式
    const autoResults = await testWithAutoForwardEnabled(targetId);
    
    // 5. 分析结果
    console.log('\n📊 测试结果分析:');
    console.log('默认模式测试:');
    console.log(`   连接建立: ${defaultResults.connected ? '✅' : '❌'}`);
    console.log(`   默认模式工作: ${defaultResults.defaultModeTest ? '✅' : '❌'}`);
    
    console.log('自动转发模式测试:');
    console.log(`   连接建立: ${autoResults.connected ? '✅' : '❌'}`);
    console.log(`   自动转发工作: ${autoResults.autoForwardWorking ? '✅' : '❌'}`);
    
    if (defaultResults.connected && defaultResults.defaultModeTest && 
        autoResults.connected && autoResults.autoForwardWorking) {
      console.log('\n🎉 自动转发功能测试成功！');
      console.log('   - 默认模式正确阻止自动转发');
      console.log('   - 自动转发模式正确启用转发');
    } else {
      console.log('\n⚠️  自动转发功能可能存在问题');
    }
    
    // 6. 清理测试目标
    await cleanupTarget(targetId);
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出测试...');
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  console.log('请确保代理服务器正在运行: npm run start:no-auto\n');
  main();
}
