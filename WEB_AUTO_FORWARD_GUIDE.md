# Web 界面自动转发功能使用指南

## 🎯 功能概述

Web 界面现在支持动态切换自动转发模式，无需重启服务器即可启用或禁用自动转发功能。

## 🚀 使用方法

### 1. 启动服务器

```bash
# 使用默认模式启动（推荐）
npm run start:no-auto
```

### 2. 打开 Web 界面

在浏览器中打开 `test-client.html` 文件。

### 3. 切换自动转发模式

在"发送自定义消息"区域，你会看到一个"自动转发模式"复选框：

- **勾选** = 启用自动转发模式
- **取消勾选** = 禁用自动转发模式

### 4. 验证配置

使用 API 调试工具中的"测试自动转发配置"按钮来验证当前配置状态。

## 🔧 功能特点

### 动态配置切换

- ✅ **实时生效** - 配置更改立即生效，无需重启服务器
- ✅ **状态同步** - Web 界面状态与服务器配置自动同步
- ✅ **错误处理** - 配置更新失败时自动恢复界面状态

### 自动状态加载

- 页面加载时自动获取当前服务器配置
- 复选框状态与服务器配置保持一致
- 显示当前模式的提示信息

### 配置验证

- 检查 Web UI 状态与服务器配置是否匹配
- 提供配置建议和故障排除提示
- 显示环境变量和配置详情

## 📋 使用流程

### 启用自动转发

1. **勾选复选框**
   - 在 Web 界面勾选"自动转发模式"
   - 系统会发送 API 请求更新服务器配置

2. **验证状态**
   - 提示信息变为绿色："自动转发模式：所有发送的消息都会自动转发到目标服务器"
   - 消息区域显示："[模式] ✅ 已启用自动转发模式"

3. **测试功能**
   - 创建目标连接
   - 连接到目标服务器
   - 发送普通消息（会自动转发）

### 禁用自动转发

1. **取消勾选复选框**
   - 在 Web 界面取消勾选"自动转发模式"
   - 系统会发送 API 请求更新服务器配置

2. **验证状态**
   - 提示信息变为灰色："默认模式：消息不会自动转发到目标服务器"
   - 消息区域显示："[模式] ❌ 已禁用自动转发模式"

3. **手动转发**
   - 使用"转发到目标服务器"功能
   - 或使用 `{"type":"forward_to_target","data":"..."}` 格式

## 🧪 测试功能

### API 调试工具

使用"测试自动转发配置"按钮可以获取详细的配置信息：

```json
{
  "currentConfig": {
    "autoForward": true,
    "autoConnect": false,
    "signalrMode": false
  },
  "environment": {
    "AUTO_FORWARD": "undefined",
    "AUTO_CONNECT": "false"
  },
  "webUIStatus": true,
  "configMatch": true,
  "recommendations": [
    "自动转发已启用，所有非控制消息将自动转发到目标服务器"
  ]
}
```

### 自动化测试

```bash
# 测试 Web API 自动转发功能
npm run test:web-api
```

## 🔍 故障排除

### 问题 1: 复选框无效果

**症状**: 勾选复选框后没有反应
**解决**: 
1. 检查浏览器控制台是否有错误
2. 确保服务器正在运行
3. 使用"测试自动转发配置"按钮检查状态

### 问题 2: 配置不匹配

**症状**: Web UI 状态与服务器配置不一致
**解决**:
1. 刷新页面重新加载配置
2. 手动切换复选框状态
3. 检查网络连接

### 问题 3: 自动转发不工作

**症状**: 启用后消息仍不转发
**解决**:
1. 确保客户端连接到目标服务器 (`?target=ID`)
2. 检查目标连接状态
3. 查看服务器控制台日志

## 💡 最佳实践

### 开发和测试

1. **使用默认启动模式**
   ```bash
   npm run start:no-auto
   ```

2. **通过 Web 界面动态切换**
   - 开发时可以随时切换模式
   - 测试不同场景的消息处理

3. **使用 API 调试工具**
   - 验证配置状态
   - 获取详细的配置信息

### 生产环境

1. **环境变量控制**
   ```bash
   AUTO_FORWARD=true npm run start:no-auto
   ```

2. **配置验证**
   - 启动后检查配置状态
   - 确保自动转发按预期工作

## 🎯 使用场景

### Phoenix Channel 应用

```javascript
// 启用自动转发模式
// 直接发送 Phoenix Channel 消息
ws.send('["6","6","driver_radio_transcriptions:session:9921","phx_join",{}]');
// 消息会自动转发到目标服务器
```

### 一般 WebSocket 应用

```javascript
// 禁用自动转发模式
// 使用手动转发
ws.send(JSON.stringify({
  type: "forward_to_target",
  data: "your message here"
}));
```

## 📊 配置状态说明

| 状态 | Web UI | 服务器 | 行为 |
|------|--------|--------|------|
| 启用 | ✅ | ✅ | 所有非控制消息自动转发 |
| 禁用 | ❌ | ❌ | 只有 `forward_to_target` 消息转发 |
| 不匹配 | ⚠️ | ⚠️ | 需要刷新或手动同步 |

通过 Web 界面，你现在可以轻松地在不同的转发模式之间切换，满足不同的使用需求！
