const WebSocket = require('ws');

// 配置
const PROXY_URL = 'ws://localhost:8080';
const CLIENT_COUNT = 3;
const TEST_INTERVAL = 3000; // 3秒发送一次消息

console.log('🎭 WebSocket 一对多广播演示\n');
console.log(`将创建 ${CLIENT_COUNT} 个客户端连接到代理服务器`);
console.log(`代理服务器地址: ${PROXY_URL}`);
console.log('每个客户端都会收到来自目标服务器的广播消息');
console.log('注意：客户端发送的消息不会转发到目标服务器\n');

const clients = [];
let messageCounter = 0;

// 创建多个客户端
for (let i = 0; i < CLIENT_COUNT; i++) {
  const clientId = i + 1;
  console.log(`🔗 正在创建客户端 #${clientId}...`);
  
  const ws = new WebSocket(PROXY_URL);
  
  const clientInfo = {
    id: clientId,
    ws: ws,
    messagesReceived: 0,
    systemMessages: 0,
    connected: false
  };
  
  ws.on('open', () => {
    clientInfo.connected = true;
    console.log(`✅ 客户端 #${clientId} 已连接`);
  });
  
  ws.on('message', (data) => {
    const messageStr = data.toString();
    
    // 检查是否是系统消息
    try {
      const parsed = JSON.parse(messageStr);
      if (parsed.type === 'system') {
        clientInfo.systemMessages++;
        console.log(`🔔 客户端 #${clientId} 收到系统消息: ${parsed.message}`);
        return;
      }
    } catch (e) {
      // 不是 JSON 格式，按普通消息处理
    }
    
    clientInfo.messagesReceived++;
    console.log(`📥 客户端 #${clientId} 收到数据消息 #${clientInfo.messagesReceived}: ${messageStr}`);
  });
  
  ws.on('close', (code, reason) => {
    clientInfo.connected = false;
    console.log(`❌ 客户端 #${clientId} 连接关闭 (${code}: ${reason || '无'})`);
  });
  
  ws.on('error', (error) => {
    console.log(`💥 客户端 #${clientId} 错误: ${error.message}`);
  });
  
  clients.push(clientInfo);
}

// 等待所有客户端连接
setTimeout(() => {
  const connectedClients = clients.filter(c => c.connected);
  console.log(`\n🎯 ${connectedClients.length}/${CLIENT_COUNT} 个客户端已连接`);
  
  if (connectedClients.length === 0) {
    console.log('❌ 没有客户端连接成功，请检查代理服务器是否运行');
    process.exit(1);
  }
  
  console.log('\n🚀 开始演示一对多广播接收...');
  console.log('每3秒会有一个客户端发送测试消息（不会转发）');
  console.log('所有客户端会收到来自目标服务器的广播数据\n');
  
  // 定期发送消息
  const sendInterval = setInterval(() => {
    const activeClients = clients.filter(c => c.connected);
    
    if (activeClients.length === 0) {
      console.log('❌ 所有客户端都已断开连接');
      clearInterval(sendInterval);
      return;
    }
    
    // 轮流让不同的客户端发送消息
    const senderIndex = messageCounter % activeClients.length;
    const sender = activeClients[senderIndex];
    messageCounter++;
    
    const message = `Hello from client #${sender.id} - Message ${messageCounter}`;
    console.log(`📤 客户端 #${sender.id} 发送测试消息: ${message} (不会转发)`);
    
    try {
      sender.ws.send(message);
    } catch (error) {
      console.log(`💥 客户端 #${sender.id} 发送失败: ${error.message}`);
    }
    
  }, TEST_INTERVAL);
  
  // 10分钟后停止演示
  setTimeout(() => {
    console.log('\n🏁 演示结束，正在关闭所有连接...');
    clearInterval(sendInterval);
    
    // 显示统计信息
    console.log('\n📊 统计信息:');
    clients.forEach(client => {
      console.log(`   客户端 #${client.id}: 接收 ${client.messagesReceived} 条数据消息, ${client.systemMessages} 条系统消息`);
    });
    
    // 关闭所有连接
    clients.forEach(client => {
      if (client.connected) {
        client.ws.close();
      }
    });
    
    setTimeout(() => {
      process.exit(0);
    }, 1000);
    
  }, 60000); // 1分钟演示
  
}, 2000);

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，正在关闭所有连接...');
  clients.forEach(client => {
    if (client.connected) {
      client.ws.close();
    }
  });
  process.exit(0);
});

process.on('uncaughtException', (error) => {
  console.error('💥 未捕获的异常:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('💥 未处理的 Promise 拒绝:', reason);
  process.exit(1);
});
