const WebSocket = require('ws');

// 配置
const API_BASE = 'http://localhost:8080';
const WS_URL = 'ws://localhost:8080';

console.log('🔧 WebSocket 连接管理 API 测试\n');

async function testAPI(endpoint, method = 'GET', body = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    if (body) {
      options.body = JSON.stringify(body);
    }
    
    const response = await fetch(`${API_BASE}${endpoint}`, options);
    const data = await response.json();
    
    console.log(`${method} ${endpoint}:`);
    console.log(`   状态: ${response.status}`);
    console.log(`   响应: ${JSON.stringify(data, null, 2)}\n`);
    
    return { success: response.ok, data };
  } catch (error) {
    console.error(`❌ API 请求失败: ${error.message}\n`);
    return { success: false, error: error.message };
  }
}

async function testWebSocketConnection() {
  console.log('🔗 测试 WebSocket 客户端连接...\n');
  
  return new Promise((resolve) => {
    const ws = new WebSocket(WS_URL);
    let messageCount = 0;
    
    const timeout = setTimeout(() => {
      console.log('⏰ WebSocket 测试超时\n');
      ws.close();
      resolve();
    }, 5000);
    
    ws.on('open', () => {
      console.log('✅ WebSocket 连接已建立');
      ws.send('Hello from API test client');
    });
    
    ws.on('message', (data) => {
      messageCount++;
      const message = data.toString();
      console.log(`📥 收到消息 ${messageCount}: ${message.substring(0, 100)}...`);
      
      if (messageCount >= 3) {
        clearTimeout(timeout);
        ws.close();
      }
    });
    
    ws.on('close', () => {
      clearTimeout(timeout);
      console.log(`❌ WebSocket 连接已关闭，共收到 ${messageCount} 条消息\n`);
      resolve();
    });
    
    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.error(`💥 WebSocket 错误: ${error.message}\n`);
      resolve();
    });
  });
}

async function main() {
  try {
    console.log('🎯 WebSocket 连接管理 API 测试套件\n');
    
    // 1. 检查服务器状态
    console.log('1️⃣ 检查服务器状态');
    await testAPI('/');
    
    // 2. 获取当前配置
    console.log('2️⃣ 获取当前配置');
    await testAPI('/api/config');
    
    // 3. 测试 WebSocket 连接（无目标服务器）
    console.log('3️⃣ 测试客户端连接（无目标服务器）');
    await testWebSocketConnection();
    
    // 4. 连接到 WebSocket 目标服务器
    console.log('4️⃣ 连接到 WebSocket 目标服务器');
    const connectResult = await testAPI('/api/connect', 'POST', {
      url: 'ws://echo.websocket.org'
    });
    
    if (connectResult.success) {
      // 等待连接建立
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 5. 检查连接状态
      console.log('5️⃣ 检查连接状态');
      await testAPI('/status');
      
      // 6. 测试 WebSocket 连接（有目标服务器）
      console.log('6️⃣ 测试客户端连接（有目标服务器）');
      await testWebSocketConnection();
      
      // 7. 断开连接
      console.log('7️⃣ 断开目标服务器连接');
      await testAPI('/api/disconnect', 'POST');
    }
    
    // 8. 测试 SignalR 连接
    console.log('8️⃣ 测试 SignalR 连接');
    const signalrResult = await testAPI('/api/connect', 'POST', {
      signalr: {
        url: 'livetiming.formula1.com/signalr',
        hub: 'Streaming',
        protocol: '1.5'
      }
    });
    
    if (signalrResult.success) {
      // 等待连接建立
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 9. 测试 SignalR 订阅
      console.log('9️⃣ 测试 SignalR 订阅');
      await testAPI('/signalr/subscribe', 'POST');
      
      // 10. 测试 WebSocket 连接（SignalR 模式）
      console.log('🔟 测试客户端连接（SignalR 模式）');
      await testWebSocketConnection();
      
      // 11. 最终断开连接
      console.log('1️⃣1️⃣ 最终断开连接');
      await testAPI('/api/disconnect', 'POST');
    }
    
    // 12. 更新配置测试
    console.log('1️⃣2️⃣ 测试配置更新');
    await testAPI('/api/config', 'PUT', {
      autoReconnect: false
    });
    
    // 13. 最终状态检查
    console.log('1️⃣3️⃣ 最终状态检查');
    await testAPI('/api/config');
    
    console.log('🎉 API 管理测试完成！');
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
    process.exit(1);
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出测试...');
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  main();
}
