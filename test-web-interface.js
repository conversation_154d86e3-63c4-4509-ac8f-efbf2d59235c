const http = require('http');

console.log('🌐 测试 Web 界面访问\n');

function httpRequest(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const urlParts = new URL(url);
    
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: responseData
        });
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testWebInterface() {
  try {
    console.log('1. 测试主页访问...');
    const homeResponse = await httpRequest('http://localhost:8080/');
    
    if (homeResponse.status === 200) {
      console.log('   ✅ 主页访问成功');
      
      // 检查是否返回 HTML 内容
      if (homeResponse.headers['content-type']?.includes('text/html')) {
        console.log('   ✅ 返回 HTML 内容');
      } else {
        console.log('   ⚠️  返回的不是 HTML 内容');
      }
      
      // 检查是否包含测试客户端内容
      if (homeResponse.data.includes('WebSocket 转发代理测试客户端')) {
        console.log('   ✅ 包含测试客户端内容');
      } else {
        console.log('   ❌ 不包含测试客户端内容');
      }
    } else {
      console.log(`   ❌ 主页访问失败，状态码: ${homeResponse.status}`);
    }
    
    console.log('\n2. 测试 API 端点访问...');
    const apiResponse = await httpRequest('http://localhost:8080/api');
    
    if (apiResponse.status === 200) {
      console.log('   ✅ API 端点访问成功');
      
      try {
        const apiData = JSON.parse(apiResponse.data);
        console.log(`   📊 服务器名称: ${apiData.name}`);
        console.log(`   📊 版本: ${apiData.version}`);
        console.log(`   📊 模式: ${apiData.mode}`);
      } catch (e) {
        console.log('   ⚠️  API 响应不是有效的 JSON');
      }
    } else {
      console.log(`   ❌ API 端点访问失败，状态码: ${apiResponse.status}`);
    }
    
    console.log('\n3. 测试配置 API...');
    const configResponse = await httpRequest('http://localhost:8080/api/config');
    
    if (configResponse.status === 200) {
      console.log('   ✅ 配置 API 访问成功');
      
      try {
        const configData = JSON.parse(configResponse.data);
        console.log(`   ⚙️  自动转发: ${configData.autoForward ? '启用' : '禁用'}`);
        console.log(`   ⚙️  自动连接: ${configData.autoConnect ? '启用' : '禁用'}`);
      } catch (e) {
        console.log('   ⚠️  配置响应不是有效的 JSON');
      }
    } else {
      console.log(`   ❌ 配置 API 访问失败，状态码: ${configResponse.status}`);
    }
    
    console.log('\n4. 测试配置更新 API...');
    const updateResponse = await httpRequest('http://localhost:8080/api/config', 'PUT', {
      autoForward: true
    });
    
    if (updateResponse.status === 200) {
      console.log('   ✅ 配置更新 API 正常工作');
      
      try {
        const updateData = JSON.parse(updateResponse.data);
        if (updateData.success) {
          console.log('   ✅ 配置更新成功');
        } else {
          console.log('   ❌ 配置更新失败');
        }
      } catch (e) {
        console.log('   ⚠️  更新响应不是有效的 JSON');
      }
    } else {
      console.log(`   ❌ 配置更新失败，状态码: ${updateResponse.status}`);
    }
    
    console.log('\n5. 恢复配置...');
    await httpRequest('http://localhost:8080/api/config', 'PUT', {
      autoForward: false
    });
    console.log('   ✅ 配置已恢复');
    
    console.log('\n📋 测试总结:');
    console.log('   - Web 界面现在通过 HTTP 服务器提供');
    console.log('   - 主页访问返回测试客户端 HTML');
    console.log('   - API 端点正常工作');
    console.log('   - 配置更新功能正常');
    console.log('   - 不再有 CORS 问题');
    
    console.log('\n🎉 Web 界面修复成功！');
    console.log('现在可以通过 http://localhost:8080 访问完整功能的 Web 界面');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.log('\n请确保服务器正在运行: npm run start:no-auto');
  }
}

console.log('请确保服务器正在运行: npm run start:no-auto');
console.log('此测试将验证 Web 界面的 HTTP 访问功能\n');

testWebInterface();
