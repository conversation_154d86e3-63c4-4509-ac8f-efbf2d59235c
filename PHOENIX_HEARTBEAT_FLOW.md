# Phoenix Channel 心跳机制流程说明

## 🔄 完整流程图

```
客户端 ──────► 代理服务器 ──────► 目标服务器 (Phoenix Channel)
                   │                      │
                   │                      │
                   │ ◄──── phx_reply ─────┘
                   │       status: "ok"
                   │
                   │ (检测到 ok 状态)
                   │
                   ▼
              启动心跳定时器
                   │
                   │ (每秒自动发送)
                   │
                   ├──── heartbeat ──────► 目标服务器
                   ├──── heartbeat ──────► 目标服务器
                   ├──── heartbeat ──────► 目标服务器
                   └──── heartbeat ──────► 目标服务器
                          (持续发送)
```

## 📋 详细流程说明

### 1. 初始连接阶段
```
客户端 → 代理服务器: 连接请求
代理服务器 → 目标服务器: 建立 WebSocket 连接
```

### 2. Phoenix Channel 加入阶段
```
客户端 → 代理服务器: {"forward": true, "data": ["6", "6", "channel", "phx_join", {}]}
代理服务器 → 目标服务器: ["6", "6", "channel", "phx_join", {}]
```

### 3. 心跳触发阶段
```
目标服务器 → 代理服务器: ["6", "6", "channel", "phx_reply", {"status": "ok", "response": {}}]
代理服务器: 检测到 phx_reply 且 status === "ok"
代理服务器: 启动心跳定时器
```

### 4. 心跳维持阶段
```
代理服务器 → 目标服务器: [null, "7", "phoenix", "heartbeat", {}]   (第 1 秒)
代理服务器 → 目标服务器: [null, "8", "phoenix", "heartbeat", {}]   (第 2 秒)
代理服务器 → 目标服务器: [null, "9", "phoenix", "heartbeat", {}]   (第 3 秒)
... (持续发送，保持连接)
```

## 🎯 关键要点

### ✅ 心跳发送方
- **发送方**: 代理服务器
- **接收方**: 目标服务器 (Phoenix Channel)
- **与客户端关系**: 无关，客户端不参与心跳过程

### ✅ 心跳目的
- **保持连接**: 维持代理服务器与目标服务器的 WebSocket 连接
- **防止超时**: 避免 Phoenix Channel 因无活动而断开连接
- **连接健康**: 确保数据流的持续性

### ✅ 触发条件
- **检测消息**: 来自目标服务器的 `phx_reply` 消息
- **状态要求**: `payload.status === "ok"`
- **自动启动**: 无需手动干预

### ✅ 心跳格式
- **固定格式**: `[null, "计数器", "phoenix", "heartbeat", {}]`
- **计数器**: 从 "7" 开始，每秒递增 1
- **发送频率**: 每秒一次

## 💻 代码实现要点

### 检测触发条件
```javascript
// 在目标连接的消息处理中
ws.on('message', (data, isBinary) => {
  const messageStr = data.toString();
  
  try {
    const parsed = JSON.parse(messageStr);
    
    // 检测 Phoenix Channel phx_reply 消息
    if (Array.isArray(parsed) && parsed.length >= 4) {
      const [, , , event, payload] = parsed;
      if (event === "phx_reply" && payload && payload.status === "ok") {
        log(`🔥 检测到 Phoenix Channel phx_reply 状态为 ok，启动心跳机制`);
        startPhoenixHeartbeat(connectionId); // 启动心跳
      }
    }
  } catch (e) {
    // 处理非 JSON 消息
  }
});
```

### 心跳发送逻辑
```javascript
function startPhoenixHeartbeat(connectionId) {
  const connectionInfo = targetConnections.get(connectionId);
  let counter = 7; // 从 7 开始
  
  const timer = setInterval(() => {
    if (connectionInfo.ws.readyState === WebSocket.OPEN) {
      // 构造心跳消息
      const heartbeatMessage = [null, counter.toString(), "phoenix", "heartbeat", {}];
      
      // 直接发送到目标服务器
      connectionInfo.ws.send(JSON.stringify(heartbeatMessage));
      
      log(`💓 代理服务器发送心跳 #${counter} 到目标服务器`);
      counter++;
    }
  }, 1000); // 每秒发送
}
```

## 🔍 日志示例

### 正常流程日志
```
[时间戳] 📥 目标连接 #1 (Phoenix Server) 收到消息:
[时间戳]    JSON 消息内容:
["6", "6", "driver_radio_transcriptions:session:9921", "phx_reply", {"status": "ok", "response": {}}]

[时间戳] 🔥 检测到 Phoenix Channel phx_reply 状态为 ok，启动心跳机制
[时间戳] 🫀 启动 Phoenix Channel 心跳 - 目标连接 #1: Phoenix Server

[时间戳] 💓 发送 Phoenix 心跳 #7 到目标 #1: [null,"7","phoenix","heartbeat",{}]
[时间戳] 💓 发送 Phoenix 心跳 #8 到目标 #1: [null,"8","phoenix","heartbeat",{}]
[时间戳] 💓 发送 Phoenix 心跳 #9 到目标 #1: [null,"9","phoenix","heartbeat",{}]
[时间戳] 💓 发送 Phoenix 心跳 #10 到目标 #1: [null,"10","phoenix","heartbeat",{}]
```

### 停止心跳日志
```
[时间戳] 💔 停止 Phoenix Channel 心跳 - 目标连接 Phoenix Server
```

## 🧪 测试验证

### 验证心跳发送
```bash
# 验证心跳格式和发送逻辑
npm run verify:phoenix

# 完整功能测试
npm run test:phoenix

# 查看心跳演示
npm run demo:phoenix
```

### 手动测试步骤
1. **启动代理服务器**
   ```bash
   npm run start:no-auto
   ```

2. **创建目标连接**
   ```bash
   curl -X POST http://localhost:8080/api/targets \
     -H "Content-Type: application/json" \
     -d '{"name": "Phoenix Server", "url": "ws://your-phoenix-server.com"}'
   ```

3. **客户端发送 phx_join**
   ```javascript
   const ws = new WebSocket('ws://localhost:8080?target=1');
   ws.send(JSON.stringify({
     forward: true,
     data: ["6", "6", "channel", "phx_join", {}]
   }));
   ```

4. **观察服务器日志**
   - 查看是否收到 phx_reply 状态 ok
   - 确认心跳自动启动
   - 验证心跳每秒发送

## 🎯 架构优势

### ✅ 自动化
- 无需手动配置心跳
- 自动检测 Phoenix Channel 协议
- 智能启动和停止

### ✅ 高效性
- 只在需要时启动心跳
- 连接断开时自动清理
- 最小化资源消耗

### ✅ 可靠性
- 严格按照 Phoenix Channel 协议
- 错误处理和重试机制
- 详细的日志记录

### ✅ 透明性
- 客户端无感知
- 不影响正常消息流
- 纯粹的连接维护

## 📊 总结

Phoenix Channel 心跳机制是代理服务器的内部功能，用于：

1. **维持连接**: 保持与 Phoenix Channel 服务器的长连接
2. **自动管理**: 检测协议状态并自动启动/停止心跳
3. **协议兼容**: 严格遵循 Phoenix Channel 心跳协议
4. **透明运行**: 对客户端完全透明，不影响业务逻辑

这确保了代理服务器能够稳定地从 Phoenix Channel 服务器接收实时数据流！🎉
