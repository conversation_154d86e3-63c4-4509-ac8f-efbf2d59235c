const WebSocket = require('ws');

console.log('🫀 测试 Phoenix Channel 心跳功能\n');

// 简单的 HTTP 请求函数
function httpRequest(url, method = 'GET', data = null) {
  const http = require('http');
  const urlParts = new URL(url);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// 模拟 Phoenix Channel 服务器
function createMockPhoenixServer() {
  return new Promise((resolve) => {
    const mockServer = new WebSocket.Server({ port: 0 });
    
    mockServer.on('connection', (ws) => {
      console.log('📡 模拟 Phoenix 服务器收到连接');
      
      let heartbeatCount = 0;
      
      ws.on('message', (data) => {
        const message = data.toString();
        console.log(`📥 模拟服务器收到消息: ${message}`);
        
        try {
          const parsed = JSON.parse(message);
          
          // 检查是否是 phx_join 消息
          if (Array.isArray(parsed) && parsed.length >= 4) {
            const [, , , event] = parsed;
            
            if (event === "phx_join") {
              console.log('🔥 收到 phx_join 消息，发送 phx_reply 状态 ok');
              
              // 发送 phx_reply 消息，状态为 ok
              const replyMessage = [
                "6", "6", "driver_radio_transcriptions:session:9921", "phx_reply",
                { "status": "ok", "response": {} }
              ];
              
              setTimeout(() => {
                ws.send(JSON.stringify(replyMessage));
                console.log(`📤 发送 phx_reply: ${JSON.stringify(replyMessage)}`);
              }, 500);
              
            } else if (event === "heartbeat") {
              heartbeatCount++;
              console.log(`💓 收到心跳 #${heartbeatCount}: ${message}`);
              
              // 验证心跳格式
              const [ref, counter, phoenix, heartbeat, payload] = parsed;
              if (ref === null && phoenix === "phoenix" && heartbeat === "heartbeat" && 
                  typeof payload === 'object' && Object.keys(payload).length === 0) {
                console.log(`   ✅ 心跳格式正确，计数器: ${counter}`);
              } else {
                console.log(`   ❌ 心跳格式错误`);
              }
            }
          }
        } catch (e) {
          console.log(`   ⚠️ 无法解析消息: ${e.message}`);
        }
      });
      
      ws.on('close', () => {
        console.log(`📡 模拟 Phoenix 服务器连接关闭，共收到 ${heartbeatCount} 个心跳`);
      });
    });
    
    const port = mockServer.address().port;
    console.log(`🚀 模拟 Phoenix 服务器启动在端口 ${port}`);
    
    resolve({
      server: mockServer,
      port: port,
      url: `ws://localhost:${port}`
    });
  });
}

async function testPhoenixHeartbeat() {
  console.log('🧪 测试 Phoenix Channel 心跳机制...\n');
  
  let mockServer = null;
  
  try {
    // 1. 创建模拟 Phoenix 服务器
    console.log('1. 创建模拟 Phoenix 服务器...');
    const mock = await createMockPhoenixServer();
    mockServer = mock.server;
    
    // 2. 在代理服务器中创建目标连接
    console.log('\n2. 创建目标连接到模拟服务器...');
    const targetResponse = await httpRequest('http://localhost:8080/api/targets', 'POST', {
      name: 'Phoenix Heartbeat Test',
      url: mock.url
    });
    
    if (!targetResponse.data.success) {
      console.log('❌ 创建目标连接失败:', targetResponse.data.error);
      return;
    }
    
    const targetId = targetResponse.data.connection.id;
    console.log(`✅ 目标连接创建成功，ID: ${targetId}`);
    
    // 3. 等待连接建立
    console.log('\n3. 等待连接建立...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 4. 连接到代理服务器并发送 phx_join 消息
    console.log('\n4. 连接到代理服务器并发送 phx_join 消息...');
    
    const proxyWs = new WebSocket(`ws://localhost:8080?target=${targetId}`);
    
    await new Promise((resolve) => {
      let heartbeatReceived = 0;
      
      const timeout = setTimeout(() => {
        console.log('\n⏰ 测试超时');
        proxyWs.close();
        resolve();
      }, 15000);
      
      proxyWs.on('open', () => {
        console.log('✅ 连接到代理服务器成功');
        
        // 发送 phx_join 消息
        const joinMessage = {
          forward: true,
          data: ["6", "6", "driver_radio_transcriptions:session:9921", "phx_join", {}]
        };
        
        console.log('📤 发送 phx_join 消息...');
        proxyWs.send(JSON.stringify(joinMessage));
      });
      
      proxyWs.on('message', (data) => {
        const message = data.toString();
        console.log(`📥 代理服务器响应: ${message.substring(0, 150)}...`);
        
        try {
          const parsed = JSON.parse(message);
          
          if (parsed.type === 'auto_forward_response' && parsed.success) {
            console.log('✅ phx_join 消息转发成功');
          }
        } catch (e) {
          // 可能是其他类型的消息
        }
      });
      
      proxyWs.on('close', () => {
        clearTimeout(timeout);
        console.log('\n❌ 代理服务器连接关闭');
        resolve();
      });
      
      proxyWs.on('error', (error) => {
        clearTimeout(timeout);
        console.error('💥 代理服务器连接错误:', error.message);
        resolve();
      });
    });
    
    // 5. 等待一段时间观察心跳
    console.log('\n5. 等待观察心跳消息...');
    await new Promise(resolve => setTimeout(resolve, 8000));
    
    // 6. 清理目标连接
    console.log('\n6. 清理目标连接...');
    await httpRequest(`http://localhost:8080/api/targets/${targetId}`, 'DELETE');
    console.log('✅ 目标连接已删除');
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
  } finally {
    // 关闭模拟服务器
    if (mockServer) {
      console.log('\n🧹 关闭模拟服务器...');
      mockServer.close();
    }
  }
}

async function main() {
  console.log('🎯 Phoenix Channel 心跳功能测试套件\n');
  console.log('📋 此测试将验证：');
  console.log('   - 检测 phx_reply 状态为 ok 的消息');
  console.log('   - 自动启动心跳机制');
  console.log('   - 心跳消息格式正确');
  console.log('   - 心跳计数器递增');
  console.log('   - 每秒发送一次心跳\n');
  
  await testPhoenixHeartbeat();
  
  console.log('\n📊 测试总结:');
  console.log('   - 检查服务器日志中的心跳启动信息');
  console.log('   - 验证模拟服务器收到的心跳消息');
  console.log('   - 确认心跳格式和计数器正确');
  
  console.log('\n💡 预期行为:');
  console.log('   1. 发送 phx_join 消息');
  console.log('   2. 收到 phx_reply 状态 ok');
  console.log('   3. 自动启动心跳机制');
  console.log('   4. 每秒发送心跳，计数器从 7 开始递增');
  
  console.log('\n🎉 Phoenix Channel 心跳测试完成！');
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出测试...');
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  console.log('请确保代理服务器正在运行: npm run start:no-auto\n');
  main();
}
