@echo off
REM F1 SignalR WebSocket 转发代理启动脚本 (Windows)

echo 🏎️  启动 F1 SignalR WebSocket 转发代理...
echo.

REM 设置环境变量
set SIGNALR_MODE=true
set SIGNALR_URL=livetiming.formula1.com/signalr
set SIGNALR_HUB=Streaming
set SIGNALR_PROTOCOL=1.5
set USER_AGENT=BestHTTP
set ENABLE_LOGGING=true
set PORT=8080

echo 📋 配置信息:
echo    - SignalR URL: %SIGNALR_URL%
echo    - Hub: %SIGNALR_HUB%
echo    - 协议版本: %SIGNALR_PROTOCOL%
echo    - 端口: %PORT%
echo.

REM 检查 Node.js 是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 Node.js
    echo 请先安装 Node.js: https://nodejs.org/
    pause
    exit /b 1
)

REM 检查依赖是否安装
if not exist "node_modules" (
    echo 📦 安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo 🚀 启动服务器...
echo 按 Ctrl+C 停止服务器
echo.

REM 启动服务器
node server.js

pause
