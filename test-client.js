const WebSocket = require('ws');

// 配置
const PROXY_URL = 'ws://localhost:8080';
const TEST_MESSAGES = [
  'Hello, World!',
  'This is a test message',
  '这是一条中文测试消息',
  JSON.stringify({ type: 'test', data: 'JSON message' }),
  '🚀 Emoji test message'
];

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testWebSocketProxy() {
  console.log('🚀 开始测试 WebSocket 广播代理 (一对多模式)...\n');

  console.log(`连接到代理服务器: ${PROXY_URL}`);
  console.log(`注意：客户端消息不会转发到目标服务器，只接收广播数据\n`);
  
  return new Promise((resolve, reject) => {
    const ws = new WebSocket(PROXY_URL);
    let messagesSent = 0;
    let messagesReceived = 0;
    let systemMessages = 0;
    let testStartTime;
    
    ws.on('open', async () => {
      console.log('✅ WebSocket 连接已建立');
      testStartTime = Date.now();
      
      // 发送测试消息（注意：这些消息不会转发到目标服务器）
      for (let i = 0; i < TEST_MESSAGES.length; i++) {
        const message = TEST_MESSAGES[i];
        console.log(`📤 发送测试消息 ${i + 1}: ${message} (不会转发)`);
        ws.send(message);
        messagesSent++;

        // 等待一段时间再发送下一条消息
        await sleep(1000);
      }
      
      // 等待所有响应
      setTimeout(() => {
        ws.close();
      }, 3000);
    });
    
    ws.on('message', (data) => {
      const messageStr = data.toString();

      // 检查是否是系统消息
      try {
        const parsed = JSON.parse(messageStr);
        if (parsed.type === 'system') {
          systemMessages++;
          console.log(`🔔 系统消息 ${systemMessages}: ${parsed.message}`);
          return;
        }
      } catch (e) {
        // 不是 JSON 格式，按普通消息处理
      }

      messagesReceived++;
      console.log(`📥 收到广播数据 ${messagesReceived}: ${messageStr}`);
    });
    
    ws.on('close', (code, reason) => {
      const testDuration = Date.now() - testStartTime;
      console.log(`\n🔌 连接已关闭 (代码: ${code}, 原因: ${reason || '无'})`);
      console.log(`\n📊 测试结果:`);
      console.log(`   - 测试持续时间: ${testDuration}ms`);
      console.log(`   - 发送消息数: ${messagesSent} (未转发)`);
      console.log(`   - 接收广播数: ${messagesReceived}`);
      console.log(`   - 系统消息数: ${systemMessages}`);
      console.log(`   - 注意: 客户端消息不转发到目标服务器`);
      
      if (messagesReceived >= 0) {
        console.log('✅ 广播接收测试完成！');
        resolve();
      } else {
        console.log('❌ 测试失败：未收到任何广播数据');
        reject(new Error('未收到广播数据'));
      }
    });
    
    ws.on('error', (error) => {
      console.error(`❌ WebSocket 错误: ${error.message}`);
      reject(error);
    });
  });
}

async function testProxyAPI() {
  console.log('\n🔍 测试代理服务器 API...\n');
  
  try {
    // 测试基本信息端点
    const response1 = await fetch('http://localhost:8080/');
    const info = await response1.json();
    console.log('📋 服务器信息:', JSON.stringify(info, null, 2));
    
    // 测试状态端点
    const response2 = await fetch('http://localhost:8080/status');
    const status = await response2.json();
    console.log('📊 服务器状态:', JSON.stringify(status, null, 2));
    
    // 测试健康检查端点
    const response3 = await fetch('http://localhost:8080/health');
    const health = await response3.json();
    console.log('💚 健康检查:', JSON.stringify(health, null, 2));
    
    console.log('✅ API 测试通过！');
  } catch (error) {
    console.error('❌ API 测试失败:', error.message);
    throw error;
  }
}

async function main() {
  try {
    console.log('🎯 WebSocket 广播代理测试套件 (一对多模式)\n');
    console.log('请确保代理服务器正在运行在 http://localhost:8080\n');
    
    // 等待用户确认
    console.log('按 Enter 键开始测试...');
    process.stdin.setRawMode(true);
    process.stdin.resume();
    await new Promise(resolve => {
      process.stdin.once('data', () => {
        process.stdin.setRawMode(false);
        resolve();
      });
    });
    
    // 测试 API
    await testProxyAPI();
    
    // 等待一段时间
    await sleep(2000);
    
    // 测试 WebSocket 转发
    await testWebSocketProxy();
    
    console.log('\n🎉 所有测试完成！');
    process.exit(0);
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message);
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('💥 未捕获的异常:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 未处理的 Promise 拒绝:', reason);
  process.exit(1);
});

// 运行测试
if (require.main === module) {
  main();
}
