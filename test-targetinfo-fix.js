// 测试 targetInfo 修复
console.log('🔧 测试 targetInfo 修复...\n');

// 模拟必要的依赖
global.WebSocket = class MockWebSocket {
  constructor() {
    this.readyState = 1; // OPEN
  }
};

global.fetch = () => Promise.resolve({ json: () => Promise.resolve({}) });

// 模拟环境变量
process.env.PORT = '8080';
process.env.AUTO_CONNECT = 'false';

try {
  // 尝试加载服务器模块
  const fs = require('fs');
  const serverCode = fs.readFileSync('server.js', 'utf8');
  
  // 检查是否包含 targetInfo 的正确定义
  const hasTargetInfoInStatus = serverCode.includes('const targetInfo = {') && 
                                serverCode.includes('targets: targetInfo');
  
  const hasTargetInfoInHome = serverCode.includes('...targetInfo');
  
  if (hasTargetInfoInStatus && hasTargetInfoInHome) {
    console.log('✅ targetInfo 变量定义检查通过');
    console.log('✅ 状态端点包含正确的 targetInfo 定义');
    console.log('✅ 主页端点正确使用 targetInfo');
  } else {
    console.log('❌ targetInfo 变量定义检查失败');
    if (!hasTargetInfoInStatus) {
      console.log('   - 状态端点缺少 targetInfo 定义');
    }
    if (!hasTargetInfoInHome) {
      console.log('   - 主页端点缺少 targetInfo 使用');
    }
  }
  
  // 检查是否移除了未定义的引用
  const hasUndefinedTargetInfo = serverCode.includes('...targetInfo') && 
                                !serverCode.includes('const targetInfo = {');
  
  if (!hasUndefinedTargetInfo) {
    console.log('✅ 未发现未定义的 targetInfo 引用');
  } else {
    console.log('❌ 仍存在未定义的 targetInfo 引用');
  }
  
  console.log('\n🎉 targetInfo 修复验证完成！');
  console.log('现在可以安全地调用所有 API 端点了。');
  
} catch (error) {
  if (error.message.includes('targetInfo is not defined')) {
    console.log('❌ targetInfo 错误仍然存在');
    console.log(`错误: ${error.message}`);
  } else {
    console.log('✅ 没有发现 targetInfo 相关错误');
    console.log(`其他错误 (正常): ${error.message.substring(0, 50)}...`);
  }
}
