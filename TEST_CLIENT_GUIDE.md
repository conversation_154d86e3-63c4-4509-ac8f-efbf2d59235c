# WebSocket 代理测试客户端 & API 调试工具使用指南

## 🎯 功能概述

`test-client.html` 是一个功能强大的 Web 界面，集成了以下功能：

- **WebSocket 客户端测试** - 测试与代理服务器的 WebSocket 连接
- **目标连接管理** - 创建、查看、删除目标连接
- **API 调试工具** - 测试所有 REST API 端点
- **服务器监控** - 实时监控服务器状态和连接数据

## 🚀 快速开始

### 1. 启动代理服务器
```bash
npm run start:no-auto
```

### 2. 打开测试客户端
在浏览器中打开 `test-client.html` 文件

### 3. 配置 API 设置
- **API 基础地址**: 默认 `http://localhost:8080`
- **API 密钥**: 如果服务器启用了认证，输入密钥

## 📋 功能详解

### 🔧 API 配置区域

配置与代理服务器的连接参数：

- **API 基础地址**: 代理服务器的 HTTP 地址
- **API 密钥**: 用于 API 认证的密钥（可选）

### 🎯 目标连接管理

#### 创建新目标连接

**WebSocket 连接:**
1. 输入连接名称（如：Echo Server）
2. 选择类型：WebSocket
3. 输入 WebSocket URL（如：ws://echo.websocket.org）
4. 点击"创建目标连接"

**SignalR 连接:**
1. 输入连接名称（如：F1 Live Data）
2. 选择类型：SignalR
3. 输入 SignalR 配置：
   - URL: livetiming.formula1.com/signalr
   - Hub: Streaming
   - 协议版本: 1.5
4. 点击"创建目标连接"

#### 管理现有连接

- **查看连接信息**: 显示 ID、类型、状态、客户端数等
- **删除连接**: 点击"删除连接"按钮
- **刷新列表**: 点击"刷新目标列表"更新数据

### 📡 WebSocket 客户端测试

#### 连接测试
1. 选择目标连接（或不选择查看可用目标）
2. 点击"连接"建立 WebSocket 连接
3. 观察连接状态和接收的消息

#### 消息测试
- **发送测试消息**: 输入自定义消息进行测试
- **SignalR 订阅**: 手动触发 SignalR 订阅
- **查询状态**: 请求服务器状态信息

### 🔍 API 调试工具

快速测试所有 API 端点：

- **获取服务器信息**: `GET /`
- **获取连接状态**: `GET /status`
- **健康检查**: `GET /health`
- **获取配置**: `GET /api/config`

API 响应会显示在专用区域，便于调试和分析。

### 📊 服务器监控

实时监控服务器运行状态：

#### 监控指标
- **服务器状态**: healthy/unhealthy
- **客户端连接数**: 当前连接的客户端数量
- **目标连接数**: 当前的目标连接数量
- **服务器运行时间**: 服务器启动后的运行时间

#### 使用方法
1. 点击"开始监控"启动实时监控
2. 数据每5秒自动更新
3. 点击"停止监控"停止更新

## 💡 使用场景

### 开发调试
- 测试新的目标连接配置
- 验证 API 端点功能
- 监控服务器性能

### 运维管理
- 实时查看服务器状态
- 管理目标连接
- 监控客户端连接数

### 功能演示
- 展示多目标连接功能
- 演示 WebSocket 消息广播
- 展示 API 管理能力

## 🔒 安全注意事项

### API 密钥
- 如果服务器启用了 API 认证，请输入正确的密钥
- 密钥输入框为密码类型，保护敏感信息

### 跨域访问
- 确保服务器 CORS 配置允许客户端域名
- 生产环境建议使用 HTTPS

### 网络安全
- 避免在公网环境暴露测试客户端
- 使用强密码保护 API 访问

## 🛠️ 故障排除

### 连接失败
1. 检查代理服务器是否运行
2. 验证 API 基础地址是否正确
3. 确认防火墙和网络设置

### API 调用失败
1. 检查 API 密钥是否正确
2. 验证服务器 CORS 配置
3. 查看浏览器控制台错误信息

### 目标连接创建失败
1. 验证目标服务器地址是否可达
2. 检查 SignalR 配置参数
3. 确认 API 权限设置

### 监控数据异常
1. 检查网络连接稳定性
2. 验证服务器响应时间
3. 查看服务器日志

## 📝 最佳实践

### 测试流程
1. 先配置 API 设置
2. 创建测试目标连接
3. 测试 WebSocket 连接
4. 启动监控观察状态

### 性能优化
- 避免创建过多目标连接
- 定期清理不需要的连接
- 监控服务器资源使用

### 安全管理
- 定期更换 API 密钥
- 限制客户端访问来源
- 监控异常连接行为

## 🔗 相关文档

- [多目标连接指南](MULTI_TARGET_GUIDE.md)
- [API 接口文档](API_GUIDE.md)
- [安全部署指南](SECURITY_GUIDE.md)

通过这个测试客户端，你可以全面测试和管理 WebSocket 代理服务器的所有功能！
