const WebSocket = require('ws');

// 配置
const API_BASE = 'http://localhost:8080';
const WS_BASE = 'ws://localhost:8080';

console.log('📨 测试消息转发功能\n');

// 简单的 HTTP 请求函数
function httpRequest(url, method = 'GET', data = null) {
  const http = require('http');
  const urlParts = new URL(url);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function createTestTarget() {
  console.log('🔗 创建测试目标连接 (Echo Server)...');
  
  try {
    const response = await httpRequest(`${API_BASE}/api/targets`, 'POST', {
      name: 'Echo Test Server',
      url: 'ws://echo.websocket.org'
    });
    
    if (response.status === 200 && response.data.success) {
      console.log(`✅ 目标连接创建成功，ID: ${response.data.connection.id}`);
      return response.data.connection.id;
    } else {
      console.log('❌ 目标连接创建失败:', response.data.error || response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ 创建目标连接时出错:', error.message);
    return null;
  }
}

async function testMessageForwarding(targetId) {
  console.log(`\n📡 测试消息转发功能 (目标 ID: ${targetId})...\n`);
  
  return new Promise((resolve) => {
    const wsUrl = `${WS_BASE}?target=${targetId}`;
    const ws = new WebSocket(wsUrl);
    
    let testResults = {
      connected: false,
      heartbeatSent: false,
      forwardSent: false,
      signalrSubscribeSent: false,
      heartbeatResponse: false,
      forwardResponse: false,
      signalrResponse: false,
      echoReceived: false
    };
    
    const timeout = setTimeout(() => {
      console.log('⏰ 测试超时');
      ws.close();
      resolve(testResults);
    }, 15000);
    
    ws.on('open', () => {
      console.log('✅ WebSocket 连接已建立');
      testResults.connected = true;
      
      // 等待一秒后开始测试
      setTimeout(() => {
        // 1. 测试心跳消息（不转发）
        console.log('1️⃣ 发送心跳消息...');
        ws.send(JSON.stringify({ type: 'ping' }));
        testResults.heartbeatSent = true;
        
        setTimeout(() => {
          // 2. 测试转发消息到目标服务器
          console.log('2️⃣ 转发消息到目标服务器...');
          ws.send(JSON.stringify({
            type: 'forward_to_target',
            data: 'Hello from forwarding test!'
          }));
          testResults.forwardSent = true;
          
          setTimeout(() => {
            // 3. 测试 SignalR 订阅（如果适用）
            console.log('3️⃣ 测试 SignalR 订阅...');
            ws.send(JSON.stringify({ type: 'signalr_subscribe' }));
            testResults.signalrSubscribeSent = true;
          }, 1000);
          
        }, 1000);
        
      }, 1000);
    });
    
    ws.on('message', (data) => {
      const message = data.toString();
      console.log(`📥 收到消息: ${message.substring(0, 100)}...`);
      
      try {
        const parsed = JSON.parse(message);
        
        switch (parsed.type) {
          case 'system':
            console.log(`   [系统] ${parsed.message}`);
            break;
            
          case 'pong':
            console.log('   ✅ 收到心跳响应');
            testResults.heartbeatResponse = true;
            break;
            
          case 'forward_response':
            console.log(`   ✅ 收到转发响应: ${parsed.message}`);
            testResults.forwardResponse = true;
            break;
            
          case 'signalr_subscribe_response':
            console.log(`   ✅ 收到 SignalR 订阅响应: ${parsed.message}`);
            testResults.signalrResponse = true;
            break;
            
          default:
            // 检查是否是来自 Echo 服务器的回显
            if (message.includes('Hello from forwarding test!')) {
              console.log('   ✅ 收到 Echo 服务器的回显消息');
              testResults.echoReceived = true;
            }
        }
      } catch (e) {
        // 检查原始消息是否包含测试内容
        if (message.includes('Hello from forwarding test!')) {
          console.log('   ✅ 收到 Echo 服务器的回显消息（原始格式）');
          testResults.echoReceived = true;
        }
      }
    });
    
    ws.on('close', () => {
      clearTimeout(timeout);
      console.log('❌ WebSocket 连接已关闭');
      resolve(testResults);
    });
    
    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.error('💥 WebSocket 错误:', error.message);
      resolve(testResults);
    });
  });
}

async function cleanupTarget(targetId) {
  console.log(`\n🧹 清理测试目标连接 #${targetId}...`);
  
  try {
    const response = await httpRequest(`${API_BASE}/api/targets/${targetId}`, 'DELETE');
    
    if (response.status === 200) {
      console.log('✅ 目标连接已删除');
    } else {
      console.log('⚠️  删除目标连接时出现问题');
    }
  } catch (error) {
    console.log('❌ 删除目标连接时出错:', error.message);
  }
}

async function main() {
  console.log('🎯 消息转发功能测试套件\n');
  
  try {
    // 1. 创建测试目标
    const targetId = await createTestTarget();
    if (!targetId) {
      console.log('❌ 无法创建测试目标，退出测试');
      return;
    }
    
    // 2. 等待目标连接建立
    console.log('⏳ 等待目标连接建立...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 3. 测试消息转发
    const results = await testMessageForwarding(targetId);
    
    // 4. 分析测试结果
    console.log('\n📊 测试结果分析:');
    console.log(`   连接建立: ${results.connected ? '✅' : '❌'}`);
    console.log(`   心跳发送: ${results.heartbeatSent ? '✅' : '❌'}`);
    console.log(`   转发发送: ${results.forwardSent ? '✅' : '❌'}`);
    console.log(`   SignalR 订阅发送: ${results.signalrSubscribeSent ? '✅' : '❌'}`);
    console.log(`   心跳响应: ${results.heartbeatResponse ? '✅' : '❌'}`);
    console.log(`   转发响应: ${results.forwardResponse ? '✅' : '❌'}`);
    console.log(`   SignalR 响应: ${results.signalrResponse ? '✅' : '❌'}`);
    console.log(`   Echo 回显: ${results.echoReceived ? '✅' : '❌'}`);
    
    const successCount = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n🎯 测试通过率: ${successCount}/${totalTests} (${Math.round(successCount/totalTests*100)}%)`);
    
    if (results.connected && results.forwardResponse && results.echoReceived) {
      console.log('\n🎉 消息转发功能测试成功！');
      console.log('   - 客户端可以连接到代理服务器');
      console.log('   - 消息可以成功转发到目标服务器');
      console.log('   - 目标服务器的响应可以正确接收');
    } else {
      console.log('\n⚠️  消息转发功能可能存在问题');
    }
    
    // 5. 清理测试目标
    await cleanupTarget(targetId);
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出测试...');
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  console.log('请确保代理服务器正在运行: npm run start:no-auto\n');
  main();
}
