const WebSocket = require('ws');

console.log('🔍 验证 Phoenix Channel 心跳格式\n');

// 简单的 HTTP 请求函数
function httpRequest(url, method = 'GET', data = null) {
  const http = require('http');
  const urlParts = new URL(url);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// 创建模拟 Phoenix 服务器来验证心跳格式
function createPhoenixMockServer() {
  return new Promise((resolve) => {
    const mockServer = new WebSocket.Server({ port: 0 });
    
    mockServer.on('connection', (ws) => {
      console.log('📡 模拟 Phoenix 服务器连接建立');
      
      let heartbeatCount = 0;
      let expectedCounter = 7; // 期望的计数器起始值
      
      ws.on('message', (data) => {
        const message = data.toString();
        
        try {
          const parsed = JSON.parse(message);
          
          if (Array.isArray(parsed) && parsed.length >= 4) {
            const [ref, counter, phoenix, event, payload] = parsed;
            
            if (event === "phx_join") {
              console.log('🔥 收到 phx_join 消息，发送 phx_reply 状态 ok');
              
              // 发送 phx_reply 消息，状态为 ok
              const replyMessage = [
                "6", "6", "driver_radio_transcriptions:session:9921", "phx_reply",
                { "status": "ok", "response": {} }
              ];
              
              setTimeout(() => {
                ws.send(JSON.stringify(replyMessage));
                console.log(`📤 发送 phx_reply: ${JSON.stringify(replyMessage)}`);
              }, 500);
              
            } else if (event === "heartbeat") {
              heartbeatCount++;
              console.log(`\n💓 收到心跳 #${heartbeatCount}:`);
              console.log(`   原始消息: ${message}`);
              console.log(`   解析结果: ${JSON.stringify(parsed, null, 2)}`);
              
              // 验证心跳格式
              let formatCorrect = true;
              let errors = [];
              
              // 检查第 1 个元素：应该是 null
              if (ref !== null) {
                formatCorrect = false;
                errors.push(`第 1 个元素应该是 null，实际是: ${ref}`);
              }
              
              // 检查第 2 个元素：应该是字符串格式的计数器
              if (counter !== expectedCounter.toString()) {
                formatCorrect = false;
                errors.push(`第 2 个元素应该是 "${expectedCounter}"，实际是: "${counter}"`);
              }
              
              // 检查第 3 个元素：应该是 "phoenix"
              if (phoenix !== "phoenix") {
                formatCorrect = false;
                errors.push(`第 3 个元素应该是 "phoenix"，实际是: "${phoenix}"`);
              }
              
              // 检查第 4 个元素：应该是 "heartbeat"
              if (event !== "heartbeat") {
                formatCorrect = false;
                errors.push(`第 4 个元素应该是 "heartbeat"，实际是: "${event}"`);
              }
              
              // 检查第 5 个元素：应该是空对象 {}
              if (typeof payload !== 'object' || payload === null || Object.keys(payload).length !== 0) {
                formatCorrect = false;
                errors.push(`第 5 个元素应该是空对象 {}，实际是: ${JSON.stringify(payload)}`);
              }
              
              // 输出验证结果
              if (formatCorrect) {
                console.log(`   ✅ 心跳格式完全正确！`);
                console.log(`   ✅ 计数器正确: ${counter} (期望: ${expectedCounter})`);
                expectedCounter++; // 递增期望的计数器
              } else {
                console.log(`   ❌ 心跳格式错误:`);
                errors.forEach(error => console.log(`      - ${error}`));
              }
              
              // 验证期望的格式
              const expectedFormat = [null, expectedCounter.toString(), "phoenix", "heartbeat", {}];
              console.log(`   📋 下次期望格式: ${JSON.stringify(expectedFormat)}`);
              
              // 如果收到足够的心跳，关闭连接
              if (heartbeatCount >= 5) {
                console.log(`\n🎯 已收到 ${heartbeatCount} 个心跳，验证完成`);
                ws.close();
              }
            }
          }
        } catch (e) {
          console.log(`   ⚠️ 无法解析消息: ${e.message}`);
        }
      });
      
      ws.on('close', () => {
        console.log(`\n📡 模拟 Phoenix 服务器连接关闭`);
        console.log(`📊 总共收到 ${heartbeatCount} 个心跳消息`);
      });
    });
    
    const port = mockServer.address().port;
    console.log(`🚀 模拟 Phoenix 服务器启动在端口 ${port}`);
    
    resolve({
      server: mockServer,
      port: port,
      url: `ws://localhost:${port}`
    });
  });
}

async function verifyPhoenixHeartbeatFormat() {
  console.log('🧪 验证 Phoenix Channel 心跳格式...\n');
  
  let mockServer = null;
  
  try {
    // 1. 创建模拟 Phoenix 服务器
    console.log('1. 创建模拟 Phoenix 服务器...');
    const mock = await createPhoenixMockServer();
    mockServer = mock.server;
    
    // 2. 在代理服务器中创建目标连接
    console.log('\n2. 创建目标连接到模拟服务器...');
    const targetResponse = await httpRequest('http://localhost:8080/api/targets', 'POST', {
      name: 'Phoenix Format Verification',
      url: mock.url
    });
    
    if (!targetResponse.data.success) {
      console.log('❌ 创建目标连接失败:', targetResponse.data.error);
      return;
    }
    
    const targetId = targetResponse.data.connection.id;
    console.log(`✅ 目标连接创建成功，ID: ${targetId}`);
    
    // 3. 等待连接建立
    console.log('\n3. 等待连接建立...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 4. 连接到代理服务器并发送 phx_join 消息
    console.log('\n4. 连接到代理服务器并发送 phx_join 消息...');
    
    const proxyWs = new WebSocket(`ws://localhost:8080?target=${targetId}`);
    
    await new Promise((resolve) => {
      const timeout = setTimeout(() => {
        console.log('\n⏰ 验证超时');
        proxyWs.close();
        resolve();
      }, 20000);
      
      proxyWs.on('open', () => {
        console.log('✅ 连接到代理服务器成功');
        
        // 发送 phx_join 消息
        const joinMessage = {
          forward: true,
          data: ["6", "6", "driver_radio_transcriptions:session:9921", "phx_join", {}]
        };
        
        console.log('📤 发送 phx_join 消息触发心跳...');
        proxyWs.send(JSON.stringify(joinMessage));
      });
      
      proxyWs.on('message', (data) => {
        const message = data.toString();
        
        try {
          const parsed = JSON.parse(message);
          
          if (parsed.type === 'auto_forward_response' && parsed.success) {
            console.log('✅ phx_join 消息转发成功，等待心跳启动...');
          }
        } catch (e) {
          // 忽略其他消息
        }
      });
      
      proxyWs.on('close', () => {
        clearTimeout(timeout);
        console.log('\n❌ 代理服务器连接关闭');
        resolve();
      });
      
      proxyWs.on('error', (error) => {
        clearTimeout(timeout);
        console.error('💥 代理服务器连接错误:', error.message);
        resolve();
      });
    });
    
    // 5. 清理目标连接
    console.log('\n5. 清理目标连接...');
    await httpRequest(`http://localhost:8080/api/targets/${targetId}`, 'DELETE');
    console.log('✅ 目标连接已删除');
    
  } catch (error) {
    console.error('💥 验证失败:', error.message);
  } finally {
    // 关闭模拟服务器
    if (mockServer) {
      console.log('\n🧹 关闭模拟服务器...');
      mockServer.close();
    }
  }
}

async function main() {
  console.log('🎯 Phoenix Channel 心跳格式验证工具\n');
  console.log('📋 此工具将验证：');
  console.log('   - 心跳消息格式: [null, "计数器", "phoenix", "heartbeat", {}]');
  console.log('   - 计数器从 "7" 开始');
  console.log('   - 每秒递增 1: "7" -> "8" -> "9" -> ...');
  console.log('   - 所有字段类型和值都正确\n');
  
  console.log('🔍 期望的心跳序列:');
  console.log('   [null, "7", "phoenix", "heartbeat", {}]');
  console.log('   [null, "8", "phoenix", "heartbeat", {}]');
  console.log('   [null, "9", "phoenix", "heartbeat", {}]');
  console.log('   [null, "10", "phoenix", "heartbeat", {}]');
  console.log('   [null, "11", "phoenix", "heartbeat", {}]\n');
  
  await verifyPhoenixHeartbeatFormat();
  
  console.log('\n📊 验证总结:');
  console.log('   - 检查每个心跳消息的格式是否完全正确');
  console.log('   - 验证计数器是否从 7 开始并正确递增');
  console.log('   - 确认所有字段类型和值都符合 Phoenix Channel 协议');
  
  console.log('\n🎉 Phoenix Channel 心跳格式验证完成！');
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出验证...');
  process.exit(0);
});

// 运行验证
if (require.main === module) {
  console.log('请确保代理服务器正在运行: npm run start:no-auto\n');
  main();
}
