const WebSocket = require('ws');

console.log('🐛 调试转发问题\n');

// 简单的 HTTP 请求函数
function httpRequest(url, method = 'GET', data = null) {
  const http = require('http');
  const urlParts = new URL(url);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function createTestTarget() {
  console.log('🔗 创建测试目标连接...');
  
  try {
    const response = await httpRequest('http://localhost:8080/api/targets', 'POST', {
      name: 'Debug Test Target',
      url: 'ws://echo.websocket.org'
    });
    
    if (response.status === 200 && response.data.success) {
      console.log(`✅ 目标连接创建成功，ID: ${response.data.connection.id}`);
      return response.data.connection.id;
    } else {
      console.log('❌ 目标连接创建失败:', response.data.error || response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ 创建目标连接时出错:', error.message);
    return null;
  }
}

async function testForwardDebug(targetId) {
  console.log(`\n🐛 调试转发问题 (目标 ID: ${targetId})...\n`);
  
  return new Promise((resolve) => {
    const wsUrl = `ws://localhost:8080?target=${targetId}`;
    const ws = new WebSocket(wsUrl);
    
    const timeout = setTimeout(() => {
      console.log('⏰ 测试超时');
      ws.close();
      resolve();
    }, 15000);
    
    ws.on('open', () => {
      console.log('✅ WebSocket 连接已建立');
      
      // 测试消息 - 这是导致问题的消息
      const testMessage = {
        forward: true,
        data: ["6", "6", "driver_radio_transcriptions:session:9921", "phx_join", {}]
      };
      
      console.log('\n📤 发送测试消息:');
      console.log('   原始对象:', JSON.stringify(testMessage, null, 2));
      
      const messageString = JSON.stringify(testMessage);
      console.log('   JSON 字符串:', messageString);
      console.log('   字符串长度:', messageString.length);
      console.log('   字符串字节数:', Buffer.from(messageString).length);
      
      // 验证 JSON 是否有效
      try {
        const parsed = JSON.parse(messageString);
        console.log('   ✅ JSON 验证成功');
        console.log('   forward 字段:', parsed.forward);
        console.log('   data 字段类型:', typeof parsed.data);
        console.log('   data 字段内容:', JSON.stringify(parsed.data));
      } catch (e) {
        console.log('   ❌ JSON 验证失败:', e.message);
      }
      
      console.log('\n📡 发送到服务器...');
      ws.send(messageString);
      
      // 等待一段时间后关闭
      setTimeout(() => {
        ws.close();
      }, 8000);
    });
    
    ws.on('message', (data) => {
      const message = data.toString();
      console.log(`\n📥 收到服务器响应:`);
      console.log(`   长度: ${message.length} 字符`);
      console.log(`   内容: ${message}`);
      
      try {
        const parsed = JSON.parse(message);
        console.log(`   类型: ${parsed.type}`);
        
        switch (parsed.type) {
          case 'system':
            console.log(`   [系统消息] ${parsed.message}`);
            if (parsed.reason) {
              console.log(`   原因: ${parsed.reason}`);
            }
            break;
            
          case 'auto_forward_response':
            console.log(`   [转发响应] 成功: ${parsed.success}`);
            console.log(`   消息: ${parsed.message}`);
            if (parsed.originalMessage) {
              console.log(`   转发内容: ${parsed.originalMessage}`);
            }
            break;
            
          default:
            console.log(`   [其他响应] ${JSON.stringify(parsed, null, 2)}`);
        }
      } catch (e) {
        console.log(`   [原始响应] ${message}`);
      }
    });
    
    ws.on('close', () => {
      clearTimeout(timeout);
      console.log('\n❌ WebSocket 连接已关闭');
      resolve();
    });
    
    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.error('💥 WebSocket 错误:', error.message);
      resolve();
    });
  });
}

async function cleanupTarget(targetId) {
  console.log(`\n🧹 清理测试目标连接 #${targetId}...`);
  
  try {
    const response = await httpRequest(`http://localhost:8080/api/targets/${targetId}`, 'DELETE');
    
    if (response.status === 200) {
      console.log('✅ 目标连接已删除');
    } else {
      console.log('⚠️  删除目标连接时出现问题');
    }
  } catch (error) {
    console.log('❌ 删除目标连接时出错:', error.message);
  }
}

async function main() {
  console.log('🎯 转发问题调试工具\n');
  console.log('📋 此工具将：');
  console.log('   - 发送包含 forward: true 的消息');
  console.log('   - 验证 JSON 格式是否正确');
  console.log('   - 检查服务器响应');
  console.log('   - 分析问题原因\n');
  
  try {
    // 1. 创建测试目标
    const targetId = await createTestTarget();
    if (!targetId) {
      console.log('❌ 无法创建测试目标，退出测试');
      return;
    }
    
    // 2. 等待目标连接建立
    console.log('⏳ 等待目标连接建立...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 3. 测试转发问题
    await testForwardDebug(targetId);
    
    // 4. 清理测试目标
    await cleanupTarget(targetId);
    
    console.log('\n📊 调试总结:');
    console.log('   请检查服务器日志中的详细调试信息');
    console.log('   特别关注 JSON 解析错误的具体原因');
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出测试...');
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  console.log('请确保代理服务器正在运行: npm run start:no-auto');
  console.log('并且查看服务器控制台的详细调试日志\n');
  main();
}
