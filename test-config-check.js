// 检查自动转发配置的简单脚本
console.log('🔍 检查自动转发配置\n');

// 模拟服务器配置检查
const CONFIG = {
  AUTO_FORWARD: process.env.AUTO_FORWARD === 'true'
};

console.log('环境变量检查:');
console.log(`  process.env.AUTO_FORWARD = "${process.env.AUTO_FORWARD}"`);
console.log(`  CONFIG.AUTO_FORWARD = ${CONFIG.AUTO_FORWARD}`);

const autoForward = process.env.AUTO_FORWARD === 'true' || CONFIG.AUTO_FORWARD;
console.log(`  最终 autoForward = ${autoForward}`);

console.log('\n建议的启动命令:');
console.log('  AUTO_FORWARD=true npm run start:no-auto');
console.log('  或者');
console.log('  npm run start:auto-forward');

console.log('\n当前进程环境变量:');
Object.keys(process.env)
  .filter(key => key.includes('AUTO') || key.includes('FORWARD'))
  .forEach(key => {
    console.log(`  ${key} = "${process.env[key]}"`);
  });

if (process.env.AUTO_FORWARD === 'true') {
  console.log('\n✅ 自动转发应该已启用');
} else {
  console.log('\n❌ 自动转发未启用');
  console.log('请使用以下命令启动服务器:');
  console.log('  AUTO_FORWARD=true node server.js');
}
