# 🚀 快速启动指南

## 1. 启动代理服务器

### 自动连接模式（默认）
```bash
npm start
```

### API 管理模式（推荐）
```bash
npm run start:no-auto
```

### SignalR 模式（F1 实时数据）
```bash
# 方法 1: 使用预配置脚本
npm run start:signalr

# 方法 2: 使用启动脚本
./start-signalr.sh        # Linux/macOS
start-signalr.bat         # Windows
```

服务器将在端口 8080 启动。API 管理模式需要通过 API 手动连接目标服务器。

## 2. 连接管理（API 模式）

### 连接到 WebSocket 服务器
```bash
curl -X POST http://localhost:8080/api/connect \
  -H "Content-Type: application/json" \
  -d '{"url": "ws://echo.websocket.org"}'
```

### 连接到 SignalR 服务器
```bash
curl -X POST http://localhost:8080/api/connect \
  -H "Content-Type: application/json" \
  -d '{
    "signalr": {
      "url": "livetiming.formula1.com/signalr",
      "hub": "Streaming",
      "protocol": "1.5"
    }
  }'
```

### 检查连接状态
```bash
curl http://localhost:8080/status
```

## 3. 测试连接

### 方法一：使用浏览器测试客户端
打开 `test-client.html` 文件在浏览器中，然后：
1. 点击"连接"按钮
2. 在消息框中输入文本
3. 点击"发送消息"
4. 观察消息日志

### 方法二：使用 Node.js 测试脚本
```bash
# WebSocket 模式测试
npm test

# SignalR 模式测试
npm run test:signalr

# API 管理功能测试
npm run test:api

# 多目标功能测试
npm run test:multi

# SignalR 初始化消息缓存测试
npm run test:signalr-init
```

### 方法三：演示一对多效果
```bash
# 一般的一对多演示
npm run demo

# F1 实时数据演示（需要 SignalR 模式）
npm run demo:f1
```

这将创建3个客户端同时连接到代理服务器，演示一对多消息广播效果。F1 演示会显示解析后的实时赛车数据。

## 4. 查看状态

访问以下 URL 查看服务器状态：
- 基本信息: http://localhost:8080/
- 详细状态: http://localhost:8080/status
- 健康检查: http://localhost:8080/health
- 当前配置: http://localhost:8080/api/config

## 4. 自定义配置

### WebSocket 模式
```bash
TARGET_WS_URL=ws://your-target-server.com npm start
```

### SignalR 模式
```bash
SIGNALR_MODE=true \
SIGNALR_URL=your-signalr-server.com/signalr \
SIGNALR_HUB=YourHub \
npm start
```

## 5. 架构说明

```
┌─────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  客户端 1   │◀───│                 │    │                 │
├─────────────┤    │                 │    │                 │
│  客户端 2   │◀───│   代理服务器     │◀──▶│   目标服务器     │
├─────────────┤    │                 │    │                 │
│  客户端 3   │◀───│                 │    │                 │
└─────────────┘    └─────────────────┘    └─────────────────┘
```

- **一对多广播架构**: 代理服务器维护与目标服务器的单一连接
- **单向数据流**: 目标服务器的消息广播给所有客户端
- **客户端消息不转发**: 客户端发送的消息不会转发到目标服务器
- **自动重连**: 目标连接断开时自动重连

## 6. 常见问题

### Q: 为什么选择一对多模式？
A: 节省资源，避免为每个客户端创建独立的目标连接，特别适合实时数据广播场景。

### Q: 客户端发送的消息会怎么处理？
A: 客户端发送的消息不会转发到目标服务器，只会记录日志并回复确认消息。这是纯接收广播模式。

### Q: 如何处理系统消息？
A: 代理服务器会发送 JSON 格式的系统消息，客户端可以通过解析 JSON 来区分系统消息和广播数据。

### Q: 目标服务器断开怎么办？
A: 代理服务器会自动重连，支持指数退避策略，并通知所有客户端连接状态变化。

## 7. 生产环境部署

### 使用 Docker
```bash
docker-compose up -d
```

### 使用 PM2
```bash
npm install -g pm2
pm2 start server.js --name websocket-proxy
```

### 环境变量配置
```bash
export PORT=8080
export TARGET_WS_URL=ws://your-production-server.com
export ENABLE_LOGGING=true
export RECONNECT_INTERVAL=5000
export MAX_RECONNECT_ATTEMPTS=10
```
