const WebSocket = require('ws');

console.log('🚀 快速测试自动转发功能\n');

// 简单的 HTTP 请求函数
function httpRequest(url, method = 'GET', data = null) {
  const http = require('http');
  const urlParts = new URL(url);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function quickTest() {
  try {
    console.log('1. 检查服务器配置...');
    const configResponse = await httpRequest('http://localhost:8080/api/config');
    console.log(`   配置状态: ${configResponse.status}`);
    if (configResponse.data.autoForward !== undefined) {
      console.log(`   自动转发: ${configResponse.data.autoForward ? '启用' : '禁用'}`);
    }
    
    console.log('\n2. 创建测试目标...');
    const targetResponse = await httpRequest('http://localhost:8080/api/targets', 'POST', {
      name: 'Quick Test Echo',
      url: 'ws://echo.websocket.org'
    });
    
    if (targetResponse.status !== 200 || !targetResponse.data.success) {
      console.log('❌ 创建目标失败');
      return;
    }
    
    const targetId = targetResponse.data.connection.id;
    console.log(`   目标 ID: ${targetId}`);
    
    console.log('\n3. 等待目标连接建立...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n4. 测试自动转发...');
    const ws = new WebSocket(`ws://localhost:8080?target=${targetId}`);
    
    let testComplete = false;
    
    const timeout = setTimeout(() => {
      if (!testComplete) {
        console.log('⏰ 测试超时');
        ws.close();
      }
    }, 10000);
    
    ws.on('open', () => {
      console.log('   ✅ WebSocket 连接建立');
      
      // 发送测试消息
      const testMessage = '["6","6","driver_radio_transcriptions:session:9921","phx_join",{}]';
      console.log(`   📤 发送测试消息: ${testMessage}`);
      ws.send(testMessage);
    });
    
    ws.on('message', (data) => {
      const message = data.toString();
      console.log(`   📥 收到响应: ${message.substring(0, 100)}...`);
      
      try {
        const parsed = JSON.parse(message);
        
        if (parsed.type === 'auto_forward_response') {
          console.log(`   ✅ 自动转发响应: ${parsed.success ? '成功' : '失败'}`);
          console.log(`   📝 消息: ${parsed.message}`);
          testComplete = true;
          clearTimeout(timeout);
          ws.close();
        } else if (parsed.type === 'system') {
          console.log(`   ⚠️  系统消息: ${parsed.message}`);
          if (parsed.reason) {
            console.log(`   🔍 原因: ${parsed.reason}`);
          }
          testComplete = true;
          clearTimeout(timeout);
          ws.close();
        }
      } catch (e) {
        // 可能是 Echo 服务器的回显
        if (message.includes('phx_join') || message.includes('driver_radio_transcriptions')) {
          console.log('   ✅ 收到 Echo 服务器回显 - 自动转发成功！');
          testComplete = true;
          clearTimeout(timeout);
          ws.close();
        }
      }
    });
    
    ws.on('close', async () => {
      console.log('\n5. 清理测试目标...');
      await httpRequest(`http://localhost:8080/api/targets/${targetId}`, 'DELETE');
      console.log('   ✅ 测试完成');
    });
    
    ws.on('error', (error) => {
      console.error(`   ❌ WebSocket 错误: ${error.message}`);
      clearTimeout(timeout);
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

console.log('请确保服务器正在运行自动转发模式:');
console.log('  npm run start:auto-forward');
console.log('');

quickTest();
