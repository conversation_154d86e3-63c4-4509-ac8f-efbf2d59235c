const { spawn } = require('child_process');
const WebSocket = require('ws');

console.log('🔄 测试服务器关闭功能...\n');

// 启动服务器
console.log('1. 启动服务器...');
const server = spawn('node', ['server.js'], {
  env: { ...process.env, AUTO_CONNECT: 'false' },
  stdio: ['pipe', 'pipe', 'pipe']
});

let serverOutput = '';
server.stdout.on('data', (data) => {
  serverOutput += data.toString();
  process.stdout.write(data);
});

server.stderr.on('data', (data) => {
  process.stderr.write(data);
});

// 等待服务器启动
setTimeout(async () => {
  try {
    console.log('\n2. 创建测试目标连接...');
    
    // 创建目标连接
    const createResponse = await fetch('http://localhost:8080/api/targets', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'Test Echo Server',
        url: 'ws://echo.websocket.org'
      })
    });
    
    if (createResponse.ok) {
      console.log('✅ 目标连接创建成功');
    } else {
      console.log('❌ 目标连接创建失败');
    }
    
    console.log('\n3. 创建测试客户端连接...');
    
    // 创建客户端连接
    const ws1 = new WebSocket('ws://localhost:8080?target=1');
    const ws2 = new WebSocket('ws://localhost:8080');
    
    let clientsConnected = 0;
    
    ws1.on('open', () => {
      console.log('✅ 客户端 1 连接成功');
      clientsConnected++;
    });
    
    ws2.on('open', () => {
      console.log('✅ 客户端 2 连接成功');
      clientsConnected++;
    });
    
    ws1.on('message', (data) => {
      console.log('📥 客户端 1 收到消息:', data.toString().substring(0, 50) + '...');
    });
    
    ws2.on('message', (data) => {
      console.log('📥 客户端 2 收到消息:', data.toString().substring(0, 50) + '...');
    });
    
    // 等待客户端连接
    setTimeout(() => {
      console.log(`\n4. 已连接 ${clientsConnected} 个客户端`);
      
      console.log('\n5. 发送 SIGINT 信号关闭服务器...');
      server.kill('SIGINT');
      
      // 监听服务器关闭
      server.on('exit', (code, signal) => {
        console.log(`\n6. 服务器已关闭 (代码: ${code}, 信号: ${signal})`);
        
        // 检查输出中是否包含预期的关闭消息
        if (serverOutput.includes('正在关闭') && 
            serverOutput.includes('个目标连接') && 
            serverOutput.includes('个客户端')) {
          console.log('✅ 优雅关闭功能正常工作！');
          console.log('✅ 所有连接都被正确关闭');
        } else {
          console.log('⚠️  关闭过程可能存在问题');
        }
        
        process.exit(0);
      });
      
    }, 2000);
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    server.kill('SIGKILL');
    process.exit(1);
  }
}, 3000);

// 处理测试脚本的中断
process.on('SIGINT', () => {
  console.log('\n\n🛑 测试被中断，清理资源...');
  server.kill('SIGKILL');
  process.exit(0);
});

// 超时保护
setTimeout(() => {
  console.log('\n⏰ 测试超时，强制退出');
  server.kill('SIGKILL');
  process.exit(1);
}, 15000);
