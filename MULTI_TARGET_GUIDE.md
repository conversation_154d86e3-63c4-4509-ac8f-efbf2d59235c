# 多目标 WebSocket 代理使用指南

## 概述

多目标模式允许代理服务器同时维护多个目标连接（WebSocket 和 SignalR），客户端可以选择连接到特定的目标服务器。这样可以实现：

- 同时连接多个不同的数据源
- 客户端按需选择数据源
- 不同类型的服务（WebSocket 和 SignalR）并存
- 灵活的数据分发策略

## 🏗️ 架构图

```
┌─────────────┐    ┌─────────────────────────────────────┐    ┌─────────────────┐
│  客户端 A   │───▶│  选择目标 #1 (WebSocket)            │◀──▶│  WebSocket 服务  │
├─────────────┤    │                                     │    └─────────────────┘
│  客户端 B   │───▶│  选择目标 #2 (SignalR)              │    ┌─────────────────┐
├─────────────┤    │        代理服务器                    │◀──▶│  SignalR 服务   │
│  客户端 C   │───▶│  选择目标 #1 (WebSocket)            │    └─────────────────┘
├─────────────┤    │                                     │    ┌─────────────────┐
│  客户端 D   │───▶│  不选择目标 (查看可用目标)           │◀──▶│  其他服务...     │
└─────────────┘    └─────────────────────────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 1. 启动代理服务器
```bash
npm run start:no-auto
```

### 2. 创建目标连接

#### 创建 WebSocket 目标
```bash
curl -X POST http://localhost:8080/api/targets \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Echo Server",
    "url": "ws://echo.websocket.org"
  }'
```

#### 创建 SignalR 目标
```bash
curl -X POST http://localhost:8080/api/targets \
  -H "Content-Type: application/json" \
  -d '{
    "name": "F1 Live Data",
    "signalr": {
      "url": "livetiming.formula1.com/signalr",
      "hub": "Streaming",
      "protocol": "1.5"
    }
  }'
```

### 3. 查看可用目标
```bash
curl http://localhost:8080/api/targets
```

响应示例：
```json
{
  "success": true,
  "targets": [
    {
      "id": 1,
      "name": "Echo Server",
      "type": "websocket",
      "url": "ws://echo.websocket.org",
      "status": "connected",
      "clientCount": 2,
      "messagesReceived": 15,
      "uptime": 30000
    },
    {
      "id": 2,
      "name": "F1 Live Data", 
      "type": "signalr",
      "url": "wss://livetiming.formula1.com/signalr/connect?...",
      "status": "connected",
      "clientCount": 1,
      "messagesReceived": 45,
      "uptime": 25000
    }
  ],
  "count": 2
}
```

### 4. 客户端连接

#### 连接到特定目标
```javascript
// 连接到目标 #1 (Echo Server)
const ws1 = new WebSocket('ws://localhost:8080?target=1');

// 连接到目标 #2 (F1 Live Data)
const ws2 = new WebSocket('ws://localhost:8080?target=2');
```

#### 不指定目标（查看可用目标）
```javascript
const ws = new WebSocket('ws://localhost:8080');
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.type === 'system' && data.availableTargets) {
    console.log('可用目标:', data.availableTargets);
  }
};
```

## 📡 API 接口

### 目标管理

#### POST /api/targets
创建新的目标连接

**WebSocket 目标:**
```json
{
  "name": "My WebSocket Server",
  "url": "ws://example.com"
}
```

**SignalR 目标:**
```json
{
  "name": "My SignalR Server",
  "signalr": {
    "url": "example.com/signalr",
    "hub": "ChatHub",
    "protocol": "1.5"
  }
}
```

#### GET /api/targets
获取所有目标连接列表

#### DELETE /api/targets/:id
删除指定的目标连接

### 客户端连接

#### 连接 URL 格式
- `ws://localhost:8080` - 不指定目标，查看可用目标
- `ws://localhost:8080?target=1` - 连接到目标 #1
- `ws://localhost:8080?target=2` - 连接到目标 #2

## 🎯 SignalR 初始化消息缓存

### 功能说明
对于 SignalR 连接，系统会自动监听并缓存第一条以 `{"R":{"Heartbeat":` 开头的初始化消息。这条消息包含了重要的初始状态信息，每个新连接的客户端都会立即收到这条缓存的消息。

### 消息格式示例
```json
{
  "R": {
    "Heartbeat": {
      "Utc": "2025-07-27T16:08:49.0216951Z",
      "_kf": true
    }
  }
}
```

### 工作流程
1. SignalR 目标连接建立后，系统监听所有消息
2. 当收到第一条以 `{"R":{"Heartbeat":` 开头的消息时，自动缓存
3. 后续每个连接到该 SignalR 目标的客户端都会立即收到这条初始化消息
4. 这确保了客户端能够获得完整的初始状态信息

### 状态查询
通过 `/api/targets` 可以查看 SignalR 目标是否已收到初始化消息：
```json
{
  "id": 1,
  "name": "F1 Live Data",
  "type": "signalr",
  "hasInitMessage": true
}
```

## 🧪 测试

### 自动化多目标测试
```bash
npm run test:multi
```

### SignalR 初始化消息测试
```bash
npm run test:signalr-init
```

### 使用浏览器测试
1. 打开 `test-client.html`
2. 点击"刷新目标列表"
3. 选择要连接的目标
4. 点击"连接"

## 💡 使用场景

### 场景 1: 多数据源聚合
```bash
# 创建股票数据源
curl -X POST http://localhost:8080/api/targets \
  -d '{"name": "Stock Data", "url": "ws://stock-api.com"}'

# 创建新闻数据源  
curl -X POST http://localhost:8080/api/targets \
  -d '{"name": "News Feed", "url": "ws://news-api.com"}'

# 客户端可以选择订阅股票或新闻数据
```

### 场景 2: 开发/生产环境切换
```bash
# 开发环境
curl -X POST http://localhost:8080/api/targets \
  -d '{"name": "Dev Server", "url": "ws://dev.example.com"}'

# 生产环境
curl -X POST http://localhost:8080/api/targets \
  -d '{"name": "Prod Server", "url": "ws://prod.example.com"}'
```

### 场景 3: 不同协议混合
```bash
# WebSocket 服务
curl -X POST http://localhost:8080/api/targets \
  -d '{"name": "Chat Server", "url": "ws://chat.example.com"}'

# SignalR 服务
curl -X POST http://localhost:8080/api/targets \
  -d '{
    "name": "Notification Hub",
    "signalr": {
      "url": "notifications.example.com/signalr",
      "hub": "NotificationHub",
      "protocol": "1.5"
    }
  }'
```

## 🔧 管理操作

### 查看服务器状态
```bash
curl http://localhost:8080/status
```

### 查看特定目标的客户端
通过状态 API 可以看到每个目标连接的客户端数量和消息统计。

### 动态添加/删除目标
可以在运行时随时添加新的目标连接或删除现有的目标连接，不会影响其他目标的客户端。

## ⚠️ 注意事项

1. **目标 ID**: 目标连接的 ID 是自动分配的递增数字
2. **客户端分组**: 客户端会根据选择的目标自动分组
3. **消息隔离**: 不同目标的消息完全隔离，不会互相干扰
4. **连接管理**: 删除目标连接会断开所有连接到该目标的客户端
5. **SignalR 订阅**: SignalR 目标会自动发送 F1 数据订阅消息
6. **SignalR 初始化消息缓存**: 系统会自动缓存第一条以 `{"R":{"Heartbeat":` 开头的初始化消息，并发送给每个新连接的客户端
7. **无目标连接**: 客户端可以不指定目标，用于查看可用目标列表

## 🎯 最佳实践

1. **命名规范**: 为目标连接使用有意义的名称
2. **监控状态**: 定期检查目标连接状态和客户端分布
3. **错误处理**: 客户端应处理目标连接断开的情况
4. **资源管理**: 及时删除不需要的目标连接
5. **负载均衡**: 可以创建多个相同类型的目标实现负载分担
