const WebSocket = require('ws');

// 配置
const API_BASE = 'http://localhost:8080';
const WS_BASE = 'ws://localhost:8080';

console.log('🎯 多目标 WebSocket 代理测试\n');

async function testAPI(endpoint, method = 'GET', body = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    if (body) {
      options.body = JSON.stringify(body);
    }
    
    const response = await fetch(`${API_BASE}${endpoint}`, options);
    const data = await response.json();
    
    console.log(`${method} ${endpoint}:`);
    console.log(`   状态: ${response.status}`);
    console.log(`   响应: ${JSON.stringify(data, null, 2)}\n`);
    
    return { success: response.ok, data };
  } catch (error) {
    console.error(`❌ API 请求失败: ${error.message}\n`);
    return { success: false, error: error.message };
  }
}

async function createTargetConnection(name, config) {
  console.log(`🔗 创建目标连接: ${name}`);
  const result = await testAPI('/api/targets', 'POST', { name, ...config });
  return result.success ? result.data.connection.id : null;
}

async function testClientConnection(targetId, clientName) {
  console.log(`👤 测试客户端连接: ${clientName} -> 目标 #${targetId || '无'}`);
  
  return new Promise((resolve) => {
    const wsUrl = targetId ? `${WS_BASE}?target=${targetId}` : WS_BASE;
    const ws = new WebSocket(wsUrl);
    let messageCount = 0;
    
    const timeout = setTimeout(() => {
      console.log(`   ⏰ ${clientName} 测试超时`);
      ws.close();
      resolve(messageCount);
    }, 8000);
    
    ws.on('open', () => {
      console.log(`   ✅ ${clientName} 连接已建立`);
      ws.send(`Hello from ${clientName}`);
    });
    
    ws.on('message', (data) => {
      messageCount++;
      const message = data.toString();
      console.log(`   📥 ${clientName} 收到消息 ${messageCount}: ${message.substring(0, 50)}...`);
      
      if (messageCount >= 3) {
        clearTimeout(timeout);
        ws.close();
      }
    });
    
    ws.on('close', () => {
      clearTimeout(timeout);
      console.log(`   ❌ ${clientName} 连接已关闭，共收到 ${messageCount} 条消息`);
      resolve(messageCount);
    });
    
    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.error(`   💥 ${clientName} 错误: ${error.message}`);
      resolve(messageCount);
    });
  });
}

async function main() {
  try {
    console.log('🎯 多目标 WebSocket 代理测试套件\n');
    
    // 1. 检查服务器状态
    console.log('1️⃣ 检查服务器状态');
    await testAPI('/');
    
    // 2. 获取当前目标列表
    console.log('2️⃣ 获取当前目标列表');
    await testAPI('/api/targets');
    
    // 3. 创建第一个目标连接（WebSocket）
    console.log('3️⃣ 创建 WebSocket 目标连接');
    const target1Id = await createTargetConnection('Echo Server', {
      url: 'ws://echo.websocket.org'
    });
    
    if (!target1Id) {
      console.error('❌ 创建 WebSocket 目标连接失败');
      return;
    }
    
    // 4. 创建第二个目标连接（SignalR）
    console.log('4️⃣ 创建 SignalR 目标连接');
    const target2Id = await createTargetConnection('F1 Live Data', {
      signalr: {
        url: 'livetiming.formula1.com/signalr',
        hub: 'Streaming',
        protocol: '1.5'
      }
    });
    
    if (!target2Id) {
      console.error('❌ 创建 SignalR 目标连接失败');
      return;
    }
    
    // 等待连接建立
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 5. 查看更新后的目标列表
    console.log('5️⃣ 查看更新后的目标列表');
    await testAPI('/api/targets');
    
    // 6. 测试客户端连接到不同目标
    console.log('6️⃣ 测试客户端连接到不同目标');
    
    const promises = [
      testClientConnection(target1Id, '客户端A(Echo)'),
      testClientConnection(target2Id, '客户端B(F1)'),
      testClientConnection(target1Id, '客户端C(Echo)'),
      testClientConnection(null, '客户端D(无目标)')
    ];
    
    const results = await Promise.all(promises);
    console.log(`\n📊 客户端测试结果: ${results.map((r, i) => `客户端${String.fromCharCode(65+i)}:${r}条消息`).join(', ')}\n`);
    
    // 7. 测试目标连接状态
    console.log('7️⃣ 检查目标连接状态');
    await testAPI('/status');
    
    // 8. 删除第一个目标连接
    console.log('8️⃣ 删除 WebSocket 目标连接');
    await testAPI(`/api/targets/${target1Id}`, 'DELETE');
    
    // 9. 再次测试客户端连接
    console.log('9️⃣ 测试删除目标后的客户端连接');
    await testClientConnection(target1Id, '客户端E(已删除目标)');
    await testClientConnection(target2Id, '客户端F(F1)');
    
    // 10. 最终状态检查
    console.log('🔟 最终状态检查');
    await testAPI('/api/targets');
    
    // 11. 清理：删除剩余目标连接
    console.log('1️⃣1️⃣ 清理剩余目标连接');
    await testAPI(`/api/targets/${target2Id}`, 'DELETE');
    
    console.log('🎉 多目标测试完成！');
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
    process.exit(1);
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出测试...');
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  main();
}
