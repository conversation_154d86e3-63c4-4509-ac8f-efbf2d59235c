// 综合验证所有修复
console.log('🔧 验证所有修复...\n');

const fs = require('fs');

function checkServerCode() {
  console.log('1. 检查服务器代码语法和变量定义...');
  
  try {
    const serverCode = fs.readFileSync('server.js', 'utf8');
    
    // 检查 targetInfo 定义
    const targetInfoDefinitions = (serverCode.match(/const targetInfo = \{/g) || []).length;
    const targetInfoUsages = (serverCode.match(/\.\.\.targetInfo|targets: targetInfo/g) || []).length;
    
    console.log(`   - targetInfo 定义次数: ${targetInfoDefinitions}`);
    console.log(`   - targetInfo 使用次数: ${targetInfoUsages}`);
    
    if (targetInfoDefinitions >= 2 && targetInfoUsages >= 2) {
      console.log('   ✅ targetInfo 变量定义和使用正确');
    } else {
      console.log('   ❌ targetInfo 变量定义或使用不正确');
      return false;
    }
    
    // 检查 reconnectTimer 定义
    const hasReconnectTimer = serverCode.includes('let reconnectTimer = null');
    if (hasReconnectTimer) {
      console.log('   ✅ reconnectTimer 变量已定义');
    } else {
      console.log('   ❌ reconnectTimer 变量未定义');
      return false;
    }
    
    // 检查 signalrConnectionToken 引用
    const hasSignalrTokenRef = serverCode.includes('signalrConnectionToken') && 
                              !serverCode.includes('signalrConnectionToken = connectionToken');
    if (!hasSignalrTokenRef) {
      console.log('   ✅ 已移除未定义的 signalrConnectionToken 引用');
    } else {
      console.log('   ❌ 仍存在未定义的 signalrConnectionToken 引用');
      return false;
    }
    
    // 检查多目标函数
    const hasCreateTargetConnection = serverCode.includes('async function createTargetConnection');
    const hasGetAvailableTargets = serverCode.includes('function getAvailableTargets');
    
    if (hasCreateTargetConnection && hasGetAvailableTargets) {
      console.log('   ✅ 多目标连接函数已定义');
    } else {
      console.log('   ❌ 多目标连接函数缺失');
      return false;
    }
    
    return true;
    
  } catch (error) {
    console.log(`   ❌ 代码检查失败: ${error.message}`);
    return false;
  }
}

function checkAPIEndpoints() {
  console.log('\n2. 检查 API 端点定义...');
  
  try {
    const serverCode = fs.readFileSync('server.js', 'utf8');
    
    const endpoints = [
      "app.get('/', ",
      "app.get('/status', ",
      "app.get('/health', ",
      "app.get('/api/targets', ",
      "app.post('/api/targets', ",
      "app.delete('/api/targets/:id', ",
      "app.get('/api/config', "
    ];
    
    let allEndpointsFound = true;
    
    endpoints.forEach(endpoint => {
      if (serverCode.includes(endpoint)) {
        console.log(`   ✅ ${endpoint.replace('app.', '').replace('(', '')} 端点已定义`);
      } else {
        console.log(`   ❌ ${endpoint.replace('app.', '').replace('(', '')} 端点缺失`);
        allEndpointsFound = false;
      }
    });
    
    return allEndpointsFound;
    
  } catch (error) {
    console.log(`   ❌ API 端点检查失败: ${error.message}`);
    return false;
  }
}

function checkSecurityFeatures() {
  console.log('\n3. 检查安全功能...');
  
  try {
    const serverCode = fs.readFileSync('server.js', 'utf8');
    
    const securityFeatures = [
      'authenticateAPI',
      'checkIPConnectionLimit',
      'rateLimit',
      'helmet',
      'cors'
    ];
    
    let allFeaturesFound = true;
    
    securityFeatures.forEach(feature => {
      if (serverCode.includes(feature)) {
        console.log(`   ✅ ${feature} 安全功能已实现`);
      } else {
        console.log(`   ❌ ${feature} 安全功能缺失`);
        allFeaturesFound = false;
      }
    });
    
    return allFeaturesFound;
    
  } catch (error) {
    console.log(`   ❌ 安全功能检查失败: ${error.message}`);
    return false;
  }
}

function checkTestFiles() {
  console.log('\n4. 检查测试文件...');
  
  const testFiles = [
    'test-client.html',
    'test-api-fix.js',
    'test-multi-targets.js',
    'test-signalr-init.js',
    'test-shutdown.js'
  ];
  
  let allFilesExist = true;
  
  testFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`   ✅ ${file} 存在`);
    } else {
      console.log(`   ❌ ${file} 缺失`);
      allFilesExist = false;
    }
  });
  
  return allFilesExist;
}

function checkDocumentation() {
  console.log('\n5. 检查文档文件...');
  
  const docFiles = [
    'README.md',
    'SECURITY_GUIDE.md',
    'MULTI_TARGET_GUIDE.md',
    'TEST_CLIENT_GUIDE.md'
  ];
  
  let allDocsExist = true;
  
  docFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`   ✅ ${file} 存在`);
    } else {
      console.log(`   ❌ ${file} 缺失`);
      allDocsExist = false;
    }
  });
  
  return allDocsExist;
}

// 主验证函数
function main() {
  console.log('🎯 WebSocket 代理项目修复验证\n');
  
  const results = [
    checkServerCode(),
    checkAPIEndpoints(),
    checkSecurityFeatures(),
    checkTestFiles(),
    checkDocumentation()
  ];
  
  const passedChecks = results.filter(r => r).length;
  const totalChecks = results.length;
  
  console.log(`\n📊 验证结果: ${passedChecks}/${totalChecks} 项检查通过`);
  
  if (passedChecks === totalChecks) {
    console.log('\n🎉 所有修复验证通过！');
    console.log('项目已准备就绪，可以安全使用。');
    console.log('\n🚀 快速开始:');
    console.log('  npm run start:no-auto  # 启动服务器');
    console.log('  npm run test:api-fix   # 测试 API');
    console.log('  打开 test-client.html   # Web 界面测试');
  } else {
    console.log('\n⚠️  部分检查未通过，请检查上述错误信息。');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
