const WebSocket = require('ws');

// 配置
const API_BASE = 'http://localhost:8080';
const WS_BASE = 'ws://localhost:8080';

console.log('🏎️  SignalR 初始化消息缓存测试\n');

async function testAPI(endpoint, method = 'GET', body = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    if (body) {
      options.body = JSON.stringify(body);
    }
    
    const response = await fetch(`${API_BASE}${endpoint}`, options);
    const data = await response.json();
    
    console.log(`${method} ${endpoint}:`);
    console.log(`   状态: ${response.status}`);
    console.log(`   响应: ${JSON.stringify(data, null, 2)}\n`);
    
    return { success: response.ok, data };
  } catch (error) {
    console.error(`❌ API 请求失败: ${error.message}\n`);
    return { success: false, error: error.message };
  }
}

async function createSignalRTarget() {
  console.log('🔗 创建 SignalR 目标连接 (F1 Live Data)');
  const result = await testAPI('/api/targets', 'POST', {
    name: 'F1 Live Data',
    signalr: {
      url: 'livetiming.formula1.com/signalr',
      hub: 'Streaming',
      protocol: '1.5'
    }
  });
  
  return result.success ? result.data.connection.id : null;
}

async function testClientConnection(targetId, clientName, testDuration = 10000) {
  console.log(`👤 测试客户端: ${clientName} -> 目标 #${targetId}`);
  
  return new Promise((resolve) => {
    const wsUrl = `${WS_BASE}?target=${targetId}`;
    const ws = new WebSocket(wsUrl);
    let messageCount = 0;
    let initMessageReceived = false;
    let firstMessageTime = null;
    
    const timeout = setTimeout(() => {
      console.log(`   ⏰ ${clientName} 测试完成`);
      ws.close();
      resolve({
        messageCount,
        initMessageReceived,
        firstMessageTime
      });
    }, testDuration);
    
    ws.on('open', () => {
      console.log(`   ✅ ${clientName} 连接已建立`);
    });
    
    ws.on('message', (data) => {
      messageCount++;
      const messageStr = data.toString();
      const timestamp = new Date().toISOString();
      
      if (messageCount === 1) {
        firstMessageTime = timestamp;
      }
      
      // 检查是否是系统消息
      try {
        const parsed = JSON.parse(messageStr);
        if (parsed.type === 'system') {
          console.log(`   🔔 ${clientName} 收到系统消息: ${parsed.message}`);
          return;
        }
      } catch (e) {
        // 不是系统消息，继续处理
      }
      
      // 检查是否是 SignalR 初始化消息
      if (messageStr.startsWith('{"R":{"Heartbeat":')) {
        initMessageReceived = true;
        console.log(`   🎯 ${clientName} 收到初始化消息 #${messageCount} (${timestamp})`);
        console.log(`      消息开头: ${messageStr.substring(0, 80)}...`);
      } else {
        console.log(`   📥 ${clientName} 收到消息 #${messageCount}: ${messageStr.substring(0, 50)}...`);
      }
    });
    
    ws.on('close', () => {
      clearTimeout(timeout);
      console.log(`   ❌ ${clientName} 连接已关闭`);
      resolve({
        messageCount,
        initMessageReceived,
        firstMessageTime
      });
    });
    
    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.error(`   💥 ${clientName} 错误: ${error.message}`);
      resolve({
        messageCount,
        initMessageReceived,
        firstMessageTime
      });
    });
  });
}

async function main() {
  try {
    console.log('🎯 SignalR 初始化消息缓存测试套件\n');
    
    // 1. 检查服务器状态
    console.log('1️⃣ 检查服务器状态');
    await testAPI('/');
    
    // 2. 创建 SignalR 目标连接
    console.log('2️⃣ 创建 SignalR 目标连接');
    const targetId = await createSignalRTarget();
    
    if (!targetId) {
      console.error('❌ 创建 SignalR 目标连接失败');
      return;
    }
    
    // 3. 等待连接建立和初始化消息
    console.log('3️⃣ 等待 SignalR 连接建立和初始化消息...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 4. 检查目标连接状态
    console.log('4️⃣ 检查目标连接状态');
    const statusResult = await testAPI('/api/targets');
    if (statusResult.success) {
      const target = statusResult.data.targets.find(t => t.id === targetId);
      if (target) {
        console.log(`   目标连接状态: ${target.status}`);
        console.log(`   已收到初始化消息: ${target.hasInitMessage ? '是' : '否'}`);
        console.log(`   已收到消息数: ${target.messagesReceived}`);
      }
    }
    
    // 5. 测试第一个客户端连接（应该等待初始化消息）
    console.log('5️⃣ 测试第一个客户端连接');
    const client1Result = await testClientConnection(targetId, '客户端A', 15000);
    
    // 6. 等待一段时间确保初始化消息被缓存
    console.log('6️⃣ 等待初始化消息缓存...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 7. 测试第二个客户端连接（应该立即收到缓存的初始化消息）
    console.log('7️⃣ 测试第二个客户端连接（应该立即收到初始化消息）');
    const client2Result = await testClientConnection(targetId, '客户端B', 8000);
    
    // 8. 测试第三个客户端连接
    console.log('8️⃣ 测试第三个客户端连接');
    const client3Result = await testClientConnection(targetId, '客户端C', 8000);
    
    // 9. 分析结果
    console.log('9️⃣ 测试结果分析');
    console.log(`\n📊 测试结果总结:`);
    console.log(`   客户端A: 消息数=${client1Result.messageCount}, 收到初始化=${client1Result.initMessageReceived ? '是' : '否'}, 首条消息时间=${client1Result.firstMessageTime}`);
    console.log(`   客户端B: 消息数=${client2Result.messageCount}, 收到初始化=${client2Result.initMessageReceived ? '是' : '否'}, 首条消息时间=${client2Result.firstMessageTime}`);
    console.log(`   客户端C: 消息数=${client3Result.messageCount}, 收到初始化=${client3Result.initMessageReceived ? '是' : '否'}, 首条消息时间=${client3Result.firstMessageTime}`);
    
    // 验证初始化消息缓存是否工作
    if (client1Result.initMessageReceived && client2Result.initMessageReceived && client3Result.initMessageReceived) {
      console.log('\n✅ 初始化消息缓存功能正常工作！');
      console.log('   - 所有客户端都收到了初始化消息');
      console.log('   - 后续客户端应该立即收到缓存的初始化消息');
    } else {
      console.log('\n⚠️  初始化消息缓存可能存在问题');
      console.log('   - 请检查 SignalR 连接和消息格式');
    }
    
    // 10. 清理：删除目标连接
    console.log('\n🔟 清理目标连接');
    await testAPI(`/api/targets/${targetId}`, 'DELETE');
    
    console.log('\n🎉 SignalR 初始化消息缓存测试完成！');
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
    process.exit(1);
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出测试...');
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  main();
}
