const WebSocket = require('ws');

// 配置
const API_BASE = 'http://localhost:8080';
const WS_BASE = 'ws://localhost:8080';

console.log('📨 测试 JSON 数据转发功能\n');

// 简单的 HTTP 请求函数
function httpRequest(url, method = 'GET', data = null) {
  const http = require('http');
  const urlParts = new URL(url);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function createTestTarget() {
  console.log('🔗 创建测试目标连接 (Echo Server)...');
  
  try {
    const response = await httpRequest(`${API_BASE}/api/targets`, 'POST', {
      name: 'JSON Echo Test Server',
      url: 'ws://echo.websocket.org'
    });
    
    if (response.status === 200 && response.data.success) {
      console.log(`✅ 目标连接创建成功，ID: ${response.data.connection.id}`);
      return response.data.connection.id;
    } else {
      console.log('❌ 目标连接创建失败:', response.data.error || response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ 创建目标连接时出错:', error.message);
    return null;
  }
}

async function testJsonForwarding(targetId) {
  console.log(`\n📡 测试 JSON 数据转发功能 (目标 ID: ${targetId})...\n`);
  
  return new Promise((resolve) => {
    const wsUrl = `${WS_BASE}?target=${targetId}`;
    const ws = new WebSocket(wsUrl);
    
    let testResults = {
      connected: false,
      simpleJsonSent: false,
      complexJsonSent: false,
      commandJsonSent: false,
      simpleJsonResponse: false,
      complexJsonResponse: false,
      commandJsonResponse: false,
      echoReceived: 0
    };
    
    const testJsonData = [
      {
        name: 'Simple JSON',
        data: { message: "Hello JSON!", timestamp: new Date().toISOString() }
      },
      {
        name: 'Complex JSON',
        data: {
          command: "processData",
          params: {
            id: 12345,
            type: "user",
            filters: {
              status: "active",
              created_after: "2024-01-01"
            }
          },
          metadata: {
            version: "1.0",
            source: "test-client"
          }
        }
      },
      {
        name: 'Command JSON',
        data: {
          action: "execute",
          target: "database",
          query: {
            table: "users",
            operation: "select",
            conditions: { id: { $gt: 100 } }
          }
        }
      }
    ];
    
    const timeout = setTimeout(() => {
      console.log('⏰ 测试超时');
      ws.close();
      resolve(testResults);
    }, 20000);
    
    ws.on('open', () => {
      console.log('✅ WebSocket 连接已建立');
      testResults.connected = true;
      
      // 等待一秒后开始测试
      setTimeout(() => {
        let testIndex = 0;
        
        function sendNextTest() {
          if (testIndex >= testJsonData.length) {
            return;
          }
          
          const test = testJsonData[testIndex];
          console.log(`${testIndex + 1}️⃣ 发送 ${test.name}...`);
          console.log(`   数据: ${JSON.stringify(test.data)}`);
          
          ws.send(JSON.stringify({
            type: 'forward_to_target',
            data: test.data
          }));
          
          // 标记发送状态
          switch (testIndex) {
            case 0: testResults.simpleJsonSent = true; break;
            case 1: testResults.complexJsonSent = true; break;
            case 2: testResults.commandJsonSent = true; break;
          }
          
          testIndex++;
          
          // 间隔发送下一个测试
          if (testIndex < testJsonData.length) {
            setTimeout(sendNextTest, 2000);
          }
        }
        
        sendNextTest();
        
      }, 1000);
    });
    
    ws.on('message', (data) => {
      const message = data.toString();
      console.log(`📥 收到消息: ${message.substring(0, 150)}...`);
      
      try {
        const parsed = JSON.parse(message);
        
        switch (parsed.type) {
          case 'system':
            console.log(`   [系统] ${parsed.message}`);
            break;
            
          case 'forward_response':
            console.log(`   ✅ 转发响应: ${parsed.message}`);
            
            // 根据原始消息判断是哪个测试的响应
            if (parsed.originalMessage) {
              const original = parsed.originalMessage;
              if (original.message === "Hello JSON!") {
                testResults.simpleJsonResponse = true;
              } else if (original.command === "processData") {
                testResults.complexJsonResponse = true;
              } else if (original.action === "execute") {
                testResults.commandJsonResponse = true;
              }
            }
            break;
            
          default:
            // 检查是否是来自 Echo 服务器的回显
            if (message.includes('Hello JSON!') || 
                message.includes('processData') || 
                message.includes('execute')) {
              console.log('   ✅ 收到 Echo 服务器的 JSON 回显');
              testResults.echoReceived++;
            }
        }
      } catch (e) {
        // 检查原始消息是否包含测试内容
        if (message.includes('Hello JSON!') || 
            message.includes('processData') || 
            message.includes('execute')) {
          console.log('   ✅ 收到 Echo 服务器的原始回显');
          testResults.echoReceived++;
        }
      }
    });
    
    ws.on('close', () => {
      clearTimeout(timeout);
      console.log('❌ WebSocket 连接已关闭');
      resolve(testResults);
    });
    
    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.error('💥 WebSocket 错误:', error.message);
      resolve(testResults);
    });
  });
}

async function cleanupTarget(targetId) {
  console.log(`\n🧹 清理测试目标连接 #${targetId}...`);
  
  try {
    const response = await httpRequest(`${API_BASE}/api/targets/${targetId}`, 'DELETE');
    
    if (response.status === 200) {
      console.log('✅ 目标连接已删除');
    } else {
      console.log('⚠️  删除目标连接时出现问题');
    }
  } catch (error) {
    console.log('❌ 删除目标连接时出错:', error.message);
  }
}

async function main() {
  console.log('🎯 JSON 数据转发功能测试套件\n');
  
  try {
    // 1. 创建测试目标
    const targetId = await createTestTarget();
    if (!targetId) {
      console.log('❌ 无法创建测试目标，退出测试');
      return;
    }
    
    // 2. 等待目标连接建立
    console.log('⏳ 等待目标连接建立...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 3. 测试 JSON 转发
    const results = await testJsonForwarding(targetId);
    
    // 4. 分析测试结果
    console.log('\n📊 JSON 转发测试结果分析:');
    console.log(`   连接建立: ${results.connected ? '✅' : '❌'}`);
    console.log(`   简单 JSON 发送: ${results.simpleJsonSent ? '✅' : '❌'}`);
    console.log(`   复杂 JSON 发送: ${results.complexJsonSent ? '✅' : '❌'}`);
    console.log(`   命令 JSON 发送: ${results.commandJsonSent ? '✅' : '❌'}`);
    console.log(`   简单 JSON 响应: ${results.simpleJsonResponse ? '✅' : '❌'}`);
    console.log(`   复杂 JSON 响应: ${results.complexJsonResponse ? '✅' : '❌'}`);
    console.log(`   命令 JSON 响应: ${results.commandJsonResponse ? '✅' : '❌'}`);
    console.log(`   Echo 回显次数: ${results.echoReceived}`);
    
    const successCount = Object.values(results).filter(v => typeof v === 'boolean' && v).length;
    const totalTests = Object.keys(results).filter(k => typeof results[k] === 'boolean').length;
    
    console.log(`\n🎯 测试通过率: ${successCount}/${totalTests} (${Math.round(successCount/totalTests*100)}%)`);
    
    if (results.connected && results.simpleJsonResponse && results.echoReceived >= 1) {
      console.log('\n🎉 JSON 数据转发功能测试成功！');
      console.log('   - 客户端可以连接到代理服务器');
      console.log('   - JSON 数据可以成功转发到目标服务器');
      console.log('   - 目标服务器的 JSON 响应可以正确接收');
      console.log('   - 支持简单和复杂的 JSON 结构');
    } else {
      console.log('\n⚠️  JSON 数据转发功能可能存在问题');
    }
    
    // 5. 清理测试目标
    await cleanupTarget(targetId);
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出测试...');
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  console.log('请确保代理服务器正在运行: npm run start:no-auto\n');
  main();
}
