const WebSocket = require('ws');

console.log('🌐 测试 Web 界面自动转发功能\n');

// 简单的 HTTP 请求函数
function httpRequest(url, method = 'GET', data = null) {
  const http = require('http');
  const urlParts = new URL(url);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testWebAutoForward() {
  try {
    console.log('1. 检查当前配置...');
    let configResponse = await httpRequest('http://localhost:8080/api/config');
    console.log(`   当前自动转发状态: ${configResponse.data.autoForward ? '启用' : '禁用'}`);
    
    console.log('\n2. 测试通过 API 启用自动转发...');
    const enableResponse = await httpRequest('http://localhost:8080/api/config', 'PUT', {
      autoForward: true
    });
    
    if (enableResponse.data.success) {
      console.log('   ✅ 自动转发已启用');
    } else {
      console.log('   ❌ 启用自动转发失败');
      return;
    }
    
    console.log('\n3. 验证配置更新...');
    configResponse = await httpRequest('http://localhost:8080/api/config');
    console.log(`   更新后自动转发状态: ${configResponse.data.autoForward ? '启用' : '禁用'}`);
    
    console.log('\n4. 创建测试目标...');
    const targetResponse = await httpRequest('http://localhost:8080/api/targets', 'POST', {
      name: 'Web Test Echo',
      url: 'ws://echo.websocket.org'
    });
    
    if (!targetResponse.data.success) {
      console.log('   ❌ 创建目标失败');
      return;
    }
    
    const targetId = targetResponse.data.connection.id;
    console.log(`   ✅ 目标创建成功，ID: ${targetId}`);
    
    console.log('\n5. 等待目标连接建立...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n6. 测试自动转发功能...');
    const ws = new WebSocket(`ws://localhost:8080?target=${targetId}`);
    
    let testComplete = false;
    
    const timeout = setTimeout(() => {
      if (!testComplete) {
        console.log('⏰ 测试超时');
        ws.close();
      }
    }, 10000);
    
    ws.on('open', () => {
      console.log('   ✅ WebSocket 连接建立');
      
      // 发送测试消息（应该自动转发）
      const testMessage = '["web","test","auto","forward","message"]';
      console.log(`   📤 发送测试消息: ${testMessage}`);
      ws.send(testMessage);
    });
    
    ws.on('message', (data) => {
      const message = data.toString();
      console.log(`   📥 收到响应: ${message.substring(0, 100)}...`);
      
      try {
        const parsed = JSON.parse(message);
        
        if (parsed.type === 'auto_forward_response') {
          console.log(`   ✅ 自动转发响应: ${parsed.success ? '成功' : '失败'}`);
          console.log(`   📝 消息: ${parsed.message}`);
          testComplete = true;
          clearTimeout(timeout);
          ws.close();
        }
      } catch (e) {
        // 可能是 Echo 服务器的回显
        if (message.includes('auto') && message.includes('forward')) {
          console.log('   ✅ 收到 Echo 服务器回显 - 自动转发成功！');
          testComplete = true;
          clearTimeout(timeout);
          ws.close();
        }
      }
    });
    
    ws.on('close', async () => {
      console.log('\n7. 测试通过 API 禁用自动转发...');
      const disableResponse = await httpRequest('http://localhost:8080/api/config', 'PUT', {
        autoForward: false
      });
      
      if (disableResponse.data.success) {
        console.log('   ✅ 自动转发已禁用');
      } else {
        console.log('   ❌ 禁用自动转发失败');
      }
      
      console.log('\n8. 清理测试目标...');
      await httpRequest(`http://localhost:8080/api/targets/${targetId}`, 'DELETE');
      console.log('   ✅ 测试完成');
      
      console.log('\n📋 测试总结:');
      console.log('   - Web 界面可以通过 API 动态切换自动转发模式');
      console.log('   - 自动转发功能在启用时正常工作');
      console.log('   - 配置更新立即生效，无需重启服务器');
      console.log('\n💡 使用方法:');
      console.log('   1. 打开 test-client.html');
      console.log('   2. 勾选/取消勾选 "自动转发模式" 复选框');
      console.log('   3. 或使用 "测试自动转发配置" 按钮检查状态');
    });
    
    ws.on('error', (error) => {
      console.error(`   ❌ WebSocket 错误: ${error.message}`);
      clearTimeout(timeout);
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

console.log('请确保服务器正在运行: npm run start:no-auto');
console.log('此测试将验证 Web 界面的自动转发切换功能\n');

testWebAutoForward();
