#!/bin/bash

# F1 SignalR WebSocket 转发代理启动脚本

echo "🏎️  启动 F1 SignalR WebSocket 转发代理..."
echo ""

# 设置环境变量
export SIGNALR_MODE=true
export SIGNALR_URL=livetiming.formula1.com/signalr
export SIGNALR_HUB=Streaming
export SIGNALR_PROTOCOL=1.5
export USER_AGENT=BestHTTP
export ENABLE_LOGGING=true
export PORT=8080

echo "📋 配置信息:"
echo "   - SignalR URL: $SIGNALR_URL"
echo "   - Hub: $SIGNALR_HUB"
echo "   - 协议版本: $SIGNALR_PROTOCOL"
echo "   - 端口: $PORT"
echo ""

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到 Node.js"
    echo "请先安装 Node.js: https://nodejs.org/"
    exit 1
fi

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

echo "🚀 启动服务器..."
echo "按 Ctrl+C 停止服务器"
echo ""

# 启动服务器
node server.js
