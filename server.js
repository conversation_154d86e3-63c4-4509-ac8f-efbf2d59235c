const WebSocket = require('ws');
const express = require('express');
const cors = require('cors');
const http = require('http');
const https = require('https');

// 配置
const CONFIG = {
  PORT: process.env.PORT || 8080,
  TARGET_WS_URL: process.env.TARGET_WS_URL || 'ws://echo.websocket.org',
  ENABLE_LOGGING: process.env.ENABLE_LOGGING !== 'false',
  RECONNECT_INTERVAL: 5000, // 重连间隔（毫秒）
  MAX_RECONNECT_ATTEMPTS: 10, // 最大重连次数
  AUTO_CONNECT: process.env.AUTO_CONNECT !== 'false', // 是否自动连接
  AUTO_FORWARD: process.env.AUTO_FORWARD === 'true', // 是否自动转发普通消息

  // SignalR 配置
  SIGNALR_MODE: process.env.SIGNALR_MODE === 'true',
  SIGNALR_URL: process.env.SIGNALR_URL || 'livetiming.formula1.com/signalr',
  SIGNALR_HUB: process.env.SIGNALR_HUB || 'Streaming',
  SIGNALR_PROTOCOL: process.env.SIGNALR_PROTOCOL || '1.5',
  USER_AGENT: process.env.USER_AGENT || 'BestHTTP'
};

// 动态配置（可通过 API 修改）
let dynamicConfig = {
  targetUrl: CONFIG.SIGNALR_MODE ? null : CONFIG.TARGET_WS_URL,
  signalrMode: CONFIG.SIGNALR_MODE,
  signalrUrl: CONFIG.SIGNALR_URL,
  signalrHub: CONFIG.SIGNALR_HUB,
  signalrProtocol: CONFIG.SIGNALR_PROTOCOL,
  autoReconnect: true
};

// 创建 Express 应用
const app = express();
app.use(cors());
app.use(express.json());

// 创建 HTTP 服务器
const server = http.createServer(app);

// 创建 WebSocket 服务器
const wss = new WebSocket.Server({ server });

// 日志函数
function log(message, ...args) {
  if (CONFIG.ENABLE_LOGGING) {
    console.log(`[${new Date().toISOString()}] ${message}`, ...args);
  }
}

// 存储客户端连接
const clients = new Map();
let clientId = 0;

// 多目标服务器连接管理
const targetConnections = new Map(); // key: connectionId, value: connection info
let connectionIdCounter = 0;

// 连接类型
const CONNECTION_TYPES = {
  WEBSOCKET: 'websocket',
  SIGNALR: 'signalr'
};

// 客户端分组管理 - 按目标连接分组
const clientGroups = new Map(); // key: targetConnectionId, value: Set of client IDs

// 全局状态（兼容旧代码）
let targetConnection = null;
let isConnecting = false;
let reconnectAttempts = 0;
let reconnectTimer = null;

// 安全管理
const ipConnections = new Map(); // IP 连接计数
const rateLimitStore = new Map(); // 速率限制存储

// SignalR 协商函数
function negotiateSignalR(signalrConfig) {
  return new Promise((resolve, reject) => {
    const hub = encodeURIComponent(JSON.stringify([{ name: signalrConfig.hub }]));
    const negotiateUrl = `https://${signalrConfig.url}/negotiate?connectionData=${hub}&clientProtocol=${signalrConfig.protocol}`;

    log(`正在进行 SignalR 协商: ${negotiateUrl}`);

    https.get(negotiateUrl, {
      headers: {
        'User-Agent': CONFIG.USER_AGENT,
        'Accept-Encoding': 'gzip,identity'
      }
    }, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          const connectionToken = result.ConnectionToken;
          const cookie = res.headers['set-cookie'];

          if (!connectionToken) {
            reject(new Error('未能获取 SignalR ConnectionToken'));
            return;
          }

          log(`SignalR 协商成功，获取到 ConnectionToken: ${connectionToken.substring(0, 20)}...`);
          resolve({ connectionToken, cookie });

        } catch (error) {
          reject(new Error(`解析 SignalR 协商响应失败: ${error.message}`));
        }
      });

    }).on('error', (error) => {
      reject(new Error(`SignalR 协商请求失败: ${error.message}`));
    });
  });
}

// 连接到目标 WebSocket 服务器（已废弃，保留以兼容旧代码）
async function connectToTarget(customUrl = null, customSignalRConfig = null) {
  // 在多目标模式下，此函数已废弃
  return { success: false, message: '此函数已废弃，请使用 createTargetConnection' };
}

// 重连调度
function scheduleReconnect() {
  if (reconnectAttempts >= CONFIG.MAX_RECONNECT_ATTEMPTS) {
    log(`已达到最大重连次数 (${CONFIG.MAX_RECONNECT_ATTEMPTS})，停止重连`);
    return;
  }

  reconnectAttempts++;
  const delay = CONFIG.RECONNECT_INTERVAL * Math.min(reconnectAttempts, 5); // 指数退避，最大5倍

  log(`${delay}ms 后尝试第 ${reconnectAttempts} 次重连...`);

  reconnectTimer = setTimeout(() => {
    connectToTarget();
  }, delay);
}

// 创建目标连接
async function createTargetConnection(config) {
  const connectionId = ++connectionIdCounter;
  const { type, name, url, signalr } = config;

  log(`正在创建目标连接 #${connectionId}: ${name} (${type})`);

  try {
    let wsUrl;
    let headers = {};
    let connectionToken = null;
    let cookie = null;

    if (type === CONNECTION_TYPES.SIGNALR) {
      log(`正在连接到 SignalR 服务器: ${signalr.url}`);

      // 进行 SignalR 协商
      const negotiationResult = await negotiateSignalR(signalr);
      connectionToken = negotiationResult.connectionToken;
      cookie = negotiationResult.cookie;

      // 构建 SignalR WebSocket URL
      const hub = encodeURIComponent(JSON.stringify([{ name: signalr.hub }]));
      wsUrl = `wss://${signalr.url}/connect?clientProtocol=${signalr.protocol}&transport=webSockets&connectionToken=${encodeURIComponent(connectionToken)}&connectionData=${hub}`;

      headers = {
        'User-Agent': CONFIG.USER_AGENT,
        'Accept-Encoding': 'gzip,identity'
      };

      if (cookie) {
        headers['Cookie'] = Array.isArray(cookie) ? cookie.join('; ') : cookie;
      }

    } else {
      wsUrl = url;
      log(`正在连接到 WebSocket 服务器: ${wsUrl}`);
    }

    const ws = new WebSocket(wsUrl, { headers });

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('连接超时'));
      }, 10000); // 10秒超时

      ws.on('open', () => {
        clearTimeout(timeout);
        log(`✅ 目标连接 #${connectionId} 成功建立`);

        // 存储连接信息
        const connectionInfo = {
          id: connectionId,
          name,
          type,
          url: wsUrl,
          ws,
          config: type === CONNECTION_TYPES.SIGNALR ? signalr : { url },
          connectionToken,
          cookie,
          startTime: Date.now(),
          messagesReceived: 0,
          autoReconnect: true,
          // SignalR 初始化消息缓存
          initMessage: null,
          hasReceivedInit: false
        };

        targetConnections.set(connectionId, connectionInfo);
        clientGroups.set(connectionId, new Set());

        // 设置消息处理
        ws.on('message', (data, isBinary) => {
          connectionInfo.messagesReceived++;
          const messageStr = data.toString();

          log(`📥 目标连接 #${connectionId} (${connectionInfo.name}) 收到消息:`);
          log(`   数据大小: ${data.length} 字节`);
          log(`   二进制模式: ${isBinary ? '是' : '否'}`);

          // 尝试解析和美化显示消息内容
          if (!isBinary) {
            try {
              const parsed = JSON.parse(messageStr);
              log(`   JSON 消息内容:`);
              log(`${JSON.stringify(parsed, null, 2)}`);
            } catch (e) {
              // 不是 JSON 格式，显示原始文本
              const preview = messageStr.length > 200 ? messageStr.substring(0, 200) + '...' : messageStr;
              log(`   文本消息内容: ${preview}`);
            }
          } else {
            log(`   二进制消息内容: [${data.length} 字节的二进制数据]`);
          }

          // 检查是否是 SignalR 初始化消息
          if (type === CONNECTION_TYPES.SIGNALR && !connectionInfo.hasReceivedInit) {
            if (messageStr.startsWith('{"R":{"Heartbeat":')) {
              connectionInfo.initMessage = data.toString();
              connectionInfo.hasReceivedInit = true;
              log(`📌 缓存 SignalR 初始化消息 (${data.length} 字节)`);
            }
          }

          // 广播给连接到此目标的客户端
          const clientCount = clientGroups.get(connectionId)?.size || 0;
          log(`📤 广播消息给 ${clientCount} 个客户端`);
          broadcastToTargetClients(connectionId, data, isBinary);
        });

        ws.on('close', (code, reason) => {
          log(`目标连接 #${connectionId} 关闭 (${code}: ${reason || '无'})`);

          // 通知连接到此目标的客户端
          broadcastToTargetClients(connectionId, JSON.stringify({
            type: 'system',
            message: `目标服务器 ${name} 连接已断开`,
            code: code,
            reason: reason || '无'
          }));

          // 清理连接
          targetConnections.delete(connectionId);
          clientGroups.delete(connectionId);
        });

        ws.on('error', (error) => {
          log(`目标连接 #${connectionId} 错误: ${error.message}`);
        });

        // 如果是 SignalR 连接，自动发送订阅
        if (type === CONNECTION_TYPES.SIGNALR) {
          setTimeout(() => {
            sendSignalRSubscriptionToTarget(connectionId);
          }, 1000);
        }

        resolve(connectionInfo);
      });

      ws.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });

  } catch (error) {
    log(`创建目标连接失败: ${error.message}`);
    throw error;
  }
}

// 广播消息给所有客户端
function broadcastToClients(data, isBinary = false) {
  const deadClients = [];

  clients.forEach((clientInfo, id) => {
    const client = clientInfo.ws;
    if (client.readyState === WebSocket.OPEN) {
      try {
        client.send(data, { binary: isBinary });
        clientInfo.messagesReceived++;
      } catch (error) {
        log(`向客户端 #${id} 发送消息失败: ${error.message}`);
        deadClients.push(id);
      }
    } else {
      deadClients.push(id);
    }
  });

  // 清理断开的客户端
  deadClients.forEach(id => {
    clients.delete(id);
    log(`清理断开的客户端 #${id}`);
  });
}

// 广播消息给特定目标连接的客户端
function broadcastToTargetClients(targetConnectionId, data, isBinary = false) {
  const clientSet = clientGroups.get(targetConnectionId);
  if (!clientSet) return;

  const deadClients = [];

  clientSet.forEach(clientId => {
    const clientInfo = clients.get(clientId);
    if (clientInfo && clientInfo.ws.readyState === WebSocket.OPEN) {
      try {
        clientInfo.ws.send(data, { binary: isBinary });
        clientInfo.messagesReceived++;
      } catch (error) {
        log(`向客户端 #${clientId} 发送消息失败: ${error.message}`);
        deadClients.push(clientId);
      }
    } else {
      deadClients.push(clientId);
    }
  });

  // 清理断开的客户端
  deadClients.forEach(clientId => {
    clientSet.delete(clientId);
    clients.delete(clientId);
    log(`清理断开的客户端 #${clientId}`);
  });
}

// 发送 SignalR 订阅消息到特定目标连接
function sendSignalRSubscriptionToTarget(targetConnectionId) {
  const connectionInfo = targetConnections.get(targetConnectionId);
  if (!connectionInfo || connectionInfo.type !== CONNECTION_TYPES.SIGNALR) {
    return false;
  }

  if (connectionInfo.ws.readyState !== WebSocket.OPEN) {
    return false;
  }

  const subscriptionMessage = {
    H: connectionInfo.config.hub,
    M: "Subscribe",
    A: [
      [
        "Heartbeat",
        "CarData.z",
        "Position.z",
        "ExtrapolatedClock",
        "TimingStats",
        "TimingAppData",
        "WeatherData",
        "TrackStatus",
        "DriverList",
        "RaceControlMessages",
        "SessionInfo",
        "SessionData",
        "LapCount",
        "TimingData",
        "TeamRadio",
      ],
    ],
    I: 1,
  };

  try {
    const messageStr = JSON.stringify(subscriptionMessage);
    connectionInfo.ws.send(messageStr);
    log(`📡 已向目标连接 #${targetConnectionId} 发送 SignalR 订阅消息`);

    // 通知连接到此目标的客户端
    broadcastToTargetClients(targetConnectionId, JSON.stringify({
      type: 'system',
      message: 'SignalR 订阅消息已发送',
      subscription: subscriptionMessage.A[0]
    }));

    return true;
  } catch (error) {
    log(`向目标连接 #${targetConnectionId} 发送 SignalR 订阅消息失败: ${error.message}`);
    return false;
  }
}

// 获取可用的目标连接列表
function getAvailableTargets() {
  return Array.from(targetConnections.values()).map(conn => ({
    id: conn.id,
    name: conn.name,
    type: conn.type,
    url: conn.url,
    status: conn.ws.readyState === WebSocket.OPEN ? 'connected' : 'disconnected',
    clientCount: clientGroups.get(conn.id)?.size || 0,
    messagesReceived: conn.messagesReceived,
    uptime: Date.now() - conn.startTime,
    // SignalR 特有信息
    hasInitMessage: conn.type === CONNECTION_TYPES.SIGNALR ? conn.hasReceivedInit : null
  }));
}

// 转发消息到目标服务器
function forwardMessageToTarget(targetConnectionId, messageData, isBinary = false) {
  const connectionInfo = targetConnections.get(targetConnectionId);
  if (!connectionInfo) {
    log(`转发失败: 目标连接 #${targetConnectionId} 不存在`);
    return false;
  }

  if (connectionInfo.ws.readyState !== WebSocket.OPEN) {
    log(`转发失败: 目标连接 #${targetConnectionId} 未连接`);
    return false;
  }

  try {
    // 如果 messageData 是对象，转换为 JSON 字符串
    let dataToSend;
    let isJsonData = false;

    if (typeof messageData === 'object' && messageData !== null) {
      dataToSend = JSON.stringify(messageData);
      isJsonData = true;
    } else {
      dataToSend = messageData;
    }

    // 详细的调试日志
    log(`🔄 转发消息到目标服务器:`);
    log(`   目标连接: #${targetConnectionId} (${connectionInfo.name})`);
    log(`   目标类型: ${connectionInfo.type}`);
    log(`   数据类型: ${isJsonData ? 'JSON' : '文本'}`);
    log(`   数据大小: ${dataToSend.length} 字节`);
    log(`   二进制模式: ${isBinary ? '是' : '否'}`);

    // 打印要转发的数据内容
    if (isJsonData) {
      log(`   JSON 数据内容:`);
      try {
        // 美化 JSON 输出用于调试
        const prettyJson = JSON.stringify(JSON.parse(dataToSend), null, 2);
        log(`${prettyJson}`);
      } catch (e) {
        log(`   ${dataToSend}`);
      }
    } else {
      log(`   文本数据内容: ${dataToSend.substring(0, 500)}${dataToSend.length > 500 ? '...' : ''}`);
    }

    connectionInfo.ws.send(dataToSend, { binary: isBinary });
    log(`✅ 消息已成功转发到目标服务器`);
    return true;

  } catch (error) {
    log(`❌ 转发消息到目标连接 #${targetConnectionId} 失败: ${error.message}`);
    return false;
  }
}

// 断开目标连接
function disconnectTarget(targetConnectionId) {
  const connectionInfo = targetConnections.get(targetConnectionId);
  if (!connectionInfo) {
    return false;
  }

  log(`断开目标连接 #${targetConnectionId}: ${connectionInfo.name}`);

  // 通知客户端
  broadcastToTargetClients(targetConnectionId, JSON.stringify({
    type: 'system',
    message: `目标服务器 ${connectionInfo.name} 即将断开连接`
  }));

  // 关闭连接
  if (connectionInfo.ws.readyState === WebSocket.OPEN) {
    connectionInfo.ws.close();
  }

  // 清理
  targetConnections.delete(targetConnectionId);
  clientGroups.delete(targetConnectionId);

  return true;
}

// 向目标服务器发送消息（多目标模式下已废弃）
// function sendToTarget(data, isBinary = false) {
//   // 此函数在多目标模式下不再使用
//   return false;
// }

// WebSocket 客户端连接处理
wss.on('connection', (clientWs, request) => {
  const connId = ++clientId;
  const clientIP = request.socket.remoteAddress;

  log(`客户端 #${connId} 连接来自 ${clientIP}`);

  // 解析查询参数以获取目标连接ID
  const url = require('url');
  const query = url.parse(request.url, true).query;
  const targetId = query.target ? parseInt(query.target) : null;

  // 存储客户端信息
  const clientInfo = {
    ws: clientWs,
    ip: clientIP,
    startTime: Date.now(),
    messagesSent: 0,
    messagesReceived: 0,
    targetConnectionId: targetId
  };

  clients.set(connId, clientInfo);

  // 如果指定了目标连接，将客户端加入对应的组
  if (targetId && targetConnections.has(targetId)) {
    clientGroups.get(targetId).add(connId);
    const targetInfo = targetConnections.get(targetId);
    log(`客户端 #${connId} 连接到目标 #${targetId}: ${targetInfo.name}`);

    // 发送连接成功消息
    const statusMessage = JSON.stringify({
      type: 'system',
      message: `已连接到目标服务器: ${targetInfo.name}`,
      clientId: connId,
      targetId: targetId,
      targetName: targetInfo.name,
      targetType: targetInfo.type
    });
    clientWs.send(statusMessage);

    // 如果是 SignalR 连接且有缓存的初始化消息，立即发送给新客户端
    if (targetInfo.type === CONNECTION_TYPES.SIGNALR && targetInfo.initMessage) {
      setTimeout(() => {
        if (clientWs.readyState === WebSocket.OPEN) {
          clientWs.send(targetInfo.initMessage);
          log(`向客户端 #${connId} 发送了缓存的 SignalR 初始化消息`);
        }
      }, 100); // 稍微延迟确保连接稳定
    }
  } else {
    // 发送可用目标列表
    const availableTargets = getAvailableTargets();
    const statusMessage = JSON.stringify({
      type: 'system',
      message: '请选择要连接的目标服务器',
      clientId: connId,
      availableTargets: availableTargets,
      usage: 'ws://localhost:8080?target=目标ID'
    });
    clientWs.send(statusMessage);
  }

  // 处理客户端消息
  clientWs.on('message', (data, isBinary) => {
    const clientInfo = clients.get(connId);
    if (clientInfo) {
      clientInfo.messagesSent++;
    }

    log(`客户端 #${connId} 发送消息 (${data.length} 字节): ${data.toString().substring(0, 100)}...`);

    // 在一对多架构中，客户端消息不转发到目标服务器
    // 而是由代理服务器本身处理

    // 如果是 SignalR 模式，检查是否是特殊的控制消息
    if (CONFIG.SIGNALR_MODE && !isBinary) {
      try {
        const messageObj = JSON.parse(data.toString());

        // 检查是否是 SignalR 订阅消息（仅用于日志记录）
        if (messageObj.H && messageObj.M === "Subscribe" && messageObj.A) {
          log(`客户端 #${connId} 尝试发送 SignalR 订阅: ${JSON.stringify(messageObj.A)}`);

          // 通知客户端：订阅由服务器管理
          clientWs.send(JSON.stringify({
            type: 'system',
            message: '订阅由代理服务器统一管理，无需客户端发送订阅消息',
            note: '服务器已自动订阅所有 F1 数据流'
          }));
          return;
        }

        // 检查其他类型的控制消息
        if (messageObj.type === 'ping') {
          // 响应 ping 消息
          clientWs.send(JSON.stringify({
            type: 'pong',
            timestamp: Date.now()
          }));
          return;
        }

        if (messageObj.type === 'status') {
          // 响应状态查询
          const targets = getAvailableTargets();
          clientWs.send(JSON.stringify({
            type: 'status_response',
            clientId: connId,
            mode: 'Multi-Target',
            totalTargets: targets.length,
            connectedTargets: targets.filter(t => t.status === 'connected').length,
            totalClients: clients.size,
            availableTargets: targets.map(t => ({
              id: t.id,
              name: t.name,
              type: t.type,
              status: t.status
            }))
          }));
          return;
        }

        if (messageObj.type === 'forward_to_target') {
          // 转发消息到目标服务器
          log(`📨 客户端 #${connId} 请求转发消息:`);
          log(`   消息类型: ${typeof messageObj.data}`);

          if (typeof messageObj.data === 'object') {
            log(`   JSON 数据: ${JSON.stringify(messageObj.data, null, 2)}`);
          } else {
            log(`   文本数据: ${messageObj.data}`);
          }

          if (clientInfo.targetConnectionId) {
            log(`   目标连接: #${clientInfo.targetConnectionId}`);
            const success = forwardMessageToTarget(clientInfo.targetConnectionId, messageObj.data, isBinary);

            const response = {
              type: 'forward_response',
              success: success,
              message: success ? '消息已转发到目标服务器' : '消息转发失败',
              targetId: clientInfo.targetConnectionId,
              originalMessage: messageObj.data
            };

            log(`📤 向客户端 #${connId} 发送转发响应: ${success ? '成功' : '失败'}`);
            clientWs.send(JSON.stringify(response));
          } else {
            log(`❌ 客户端 #${connId} 未连接到任何目标服务器`);
            clientWs.send(JSON.stringify({
              type: 'forward_response',
              success: false,
              message: '客户端未连接到目标服务器',
              originalMessage: messageObj.data
            }));
          }
          return;
        }

        if (messageObj.type === 'signalr_subscribe') {
          // 手动触发 SignalR 订阅
          if (clientInfo.targetConnectionId) {
            const success = sendSignalRSubscriptionToTarget(clientInfo.targetConnectionId);
            clientWs.send(JSON.stringify({
              type: 'signalr_subscribe_response',
              success: success,
              message: success ? 'SignalR 订阅已发送' : 'SignalR 订阅发送失败',
              targetId: clientInfo.targetConnectionId
            }));
          } else {
            clientWs.send(JSON.stringify({
              type: 'signalr_subscribe_response',
              success: false,
              message: '客户端未连接到 SignalR 目标'
            }));
          }
          return;
        }

      } catch (e) {
        // 不是 JSON 格式，按普通消息处理
        log(`客户端 #${connId} 发送的不是标准 JSON 控制消息，将作为普通消息处理`);
      }
    }

    // 对于普通消息，提供选择：转发或不转发
    // 检查是否启用了自动转发模式
    const autoForward = process.env.AUTO_FORWARD === 'true' || CONFIG.AUTO_FORWARD;

    // 调试日志
    log(`🔍 自动转发检查: process.env.AUTO_FORWARD=${process.env.AUTO_FORWARD}, CONFIG.AUTO_FORWARD=${CONFIG.AUTO_FORWARD}, autoForward=${autoForward}`);
    log(`🔍 客户端目标连接: clientInfo.targetConnectionId=${clientInfo.targetConnectionId}`);

    if (autoForward && clientInfo.targetConnectionId) {
      // 自动转发模式：直接转发到目标服务器
      log(`🔄 自动转发模式：转发普通消息到目标服务器`);
      const success = forwardMessageToTarget(clientInfo.targetConnectionId, data.toString(), isBinary);

      clientWs.send(JSON.stringify({
        type: 'auto_forward_response',
        success: success,
        message: success ? '消息已自动转发到目标服务器' : '自动转发失败',
        targetId: clientInfo.targetConnectionId,
        originalMessage: data.toString().substring(0, 100) + (data.length > 100 ? '...' : '')
      }));
    } else {
      // 默认模式：不转发，提示用户使用 forward_to_target
      let reason = '';
      if (!autoForward) {
        reason = '自动转发未启用';
      } else if (!clientInfo.targetConnectionId) {
        reason = '客户端未连接到目标服务器';
      }

      log(`❌ 不转发消息，原因: ${reason}`);

      clientWs.send(JSON.stringify({
        type: 'system',
        message: `已收到消息，但未启用自动转发。使用 {"type":"forward_to_target","data":"your_message"} 格式来转发消息`,
        receivedLength: data.length,
        clientId: connId,
        reason: reason,
        hint: '发送 {"type":"forward_to_target","data":' + JSON.stringify(data.toString()) + '} 来转发此消息'
      }));
    }
  });

  // 处理客户端断开连接
  clientWs.on('close', (code, reason) => {
    log(`客户端 #${connId} 断开连接 (${code}: ${reason || '无'})`);

    // 从目标组中移除客户端
    if (clientInfo.targetConnectionId) {
      const targetGroup = clientGroups.get(clientInfo.targetConnectionId);
      if (targetGroup) {
        targetGroup.delete(connId);
      }
    }

    clients.delete(connId);
  });

  // 处理客户端错误
  clientWs.on('error', (error) => {
    log(`客户端 #${connId} 错误: ${error.message}`);

    // 从目标组中移除客户端
    if (clientInfo.targetConnectionId) {
      const targetGroup = clientGroups.get(clientInfo.targetConnectionId);
      if (targetGroup) {
        targetGroup.delete(connId);
      }
    }

    clients.delete(connId);
  });
});

// REST API 端点
app.get('/', (req, res) => {
  // 获取目标连接信息
  const targets = getAvailableTargets();
  const connectedTargets = targets.filter(t => t.status === 'connected');

  const targetInfo = {
    mode: 'Multi-Target',
    totalTargets: targets.length,
    connectedTargets: connectedTargets.length,
    targets: targets.map(t => ({
      id: t.id,
      name: t.name,
      type: t.type,
      status: t.status
    }))
  };

  res.json({
    name: 'WebSocket 转发代理 (多目标模式)',
    version: '2.0.0',
    status: 'running',
    clientConnections: clients.size,
    ...targetInfo,
    uptime: process.uptime(),
    apiEndpoints: [
      'GET /',
      'GET /status',
      'GET /health',
      'GET /api/targets',
      'POST /api/targets',
      'DELETE /api/targets/:id',
      'POST /signalr/subscribe',
      'GET /api/config'
    ]
  });
});

// 获取连接状态
app.get('/status', (req, res) => {
  const clientList = Array.from(clients.entries()).map(([id, client]) => ({
    id,
    ip: client.ip,
    uptime: Date.now() - client.startTime,
    messagesSent: client.messagesSent,
    messagesReceived: client.messagesReceived,
    status: client.ws.readyState === WebSocket.OPEN ? 'connected' : 'disconnected'
  }));

  // 获取目标连接信息
  const targets = getAvailableTargets();
  const connectedTargets = targets.filter(t => t.status === 'connected');

  const targetInfo = {
    mode: 'Multi-Target',
    totalTargets: targets.length,
    connectedTargets: connectedTargets.length,
    targets: targets.map(t => ({
      id: t.id,
      name: t.name,
      type: t.type,
      status: t.status
    }))
  };

  res.json({
    totalClients: clients.size,
    clients: clientList,
    targets: targetInfo,
    serverUptime: process.uptime()
  });
});

// 健康检查端点
app.get('/health', (req, res) => {
  const targets = getAvailableTargets();
  const connectedTargets = targets.filter(t => t.status === 'connected');
  const isHealthy = targets.length === 0 || connectedTargets.length > 0;

  res.status(isHealthy ? 200 : 503).json({
    status: isHealthy ? 'healthy' : 'unhealthy',
    totalTargets: targets.length,
    connectedTargets: connectedTargets.length,
    clientConnections: clients.size,
    timestamp: new Date().toISOString()
  });
});

// SignalR 订阅端点
app.post('/signalr/subscribe', (req, res) => {
  const targets = getAvailableTargets();
  const signalrTargets = targets.filter(t => t.type === 'signalr' && t.status === 'connected');

  if (signalrTargets.length === 0) {
    return res.status(400).json({
      error: '没有可用的 SignalR 目标连接',
      message: '请先创建并连接 SignalR 目标'
    });
  }

  let successCount = 0;
  signalrTargets.forEach(target => {
    const success = sendSignalRSubscriptionToTarget(target.id);
    if (success) successCount++;
  });

  if (successCount > 0) {
    res.json({
      success: true,
      message: `已向 ${successCount} 个 SignalR 目标发送订阅消息`,
      targets: signalrTargets.map(t => ({ id: t.id, name: t.name })),
      timestamp: new Date().toISOString()
    });
  } else {
    res.status(503).json({
      success: false,
      error: '发送订阅消息失败',
      message: '所有 SignalR 目标连接不可用'
    });
  }
});

// 创建目标连接 API
app.post('/api/targets', async (req, res) => {
  try {
    const { name, url, signalr } = req.body;

    if (!name) {
      return res.status(400).json({
        error: '请提供连接名称',
        example: {
          websocket: { name: 'Echo Server', url: 'ws://echo.websocket.org' },
          signalr: {
            name: 'F1 Live Data',
            signalr: {
              url: 'livetiming.formula1.com/signalr',
              hub: 'Streaming',
              protocol: '1.5'
            }
          }
        }
      });
    }

    if (!url && !signalr) {
      return res.status(400).json({
        error: '请提供 url 或 signalr 配置'
      });
    }

    const config = {
      name,
      type: signalr ? CONNECTION_TYPES.SIGNALR : CONNECTION_TYPES.WEBSOCKET,
      url,
      signalr
    };

    const connectionInfo = await createTargetConnection(config);

    res.json({
      success: true,
      message: '目标连接创建成功',
      connection: {
        id: connectionInfo.id,
        name: connectionInfo.name,
        type: connectionInfo.type,
        url: connectionInfo.url,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 获取目标连接列表 API
app.get('/api/targets', (req, res) => {
  const targets = getAvailableTargets();
  res.json({
    success: true,
    targets: targets,
    count: targets.length,
    timestamp: new Date().toISOString()
  });
});

// 删除目标连接 API
app.delete('/api/targets/:id', (req, res) => {
  try {
    const targetId = parseInt(req.params.id);

    if (!targetConnections.has(targetId)) {
      return res.status(404).json({
        success: false,
        error: '目标连接不存在'
      });
    }

    const success = disconnectTarget(targetId);

    if (success) {
      res.json({
        success: true,
        message: `目标连接 #${targetId} 已断开`,
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(500).json({
        success: false,
        error: '断开连接失败'
      });
    }

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取连接配置 API
app.get('/api/config', (req, res) => {
  const targets = getAvailableTargets();

  res.json({
    mode: 'Multi-Target',
    targets: {
      total: targets.length,
      connected: targets.filter(t => t.status === 'connected').length,
      byType: {
        websocket: targets.filter(t => t.type === 'websocket').length,
        signalr: targets.filter(t => t.type === 'signalr').length
      }
    },
    clients: {
      total: clients.size,
      byTarget: Array.from(clientGroups.entries()).map(([targetId, clientSet]) => ({
        targetId,
        clientCount: clientSet.size
      }))
    },
    security: {
      apiKeyEnabled: !!CONFIG.API_KEY,
      corsEnabled: true,
      rateLimitEnabled: true
    },
    timestamp: new Date().toISOString()
  });
});

// 更新配置 API
app.put('/api/config', (req, res) => {
  try {
    const { autoReconnect } = req.body;

    if (typeof autoReconnect === 'boolean') {
      dynamicConfig.autoReconnect = autoReconnect;
    }

    res.json({
      success: true,
      message: '配置已更新',
      config: dynamicConfig,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 启动服务器
server.listen(CONFIG.PORT, () => {
  console.log(`🚀 WebSocket 转发代理服务器 (一对多模式) 启动在端口 ${CONFIG.PORT}`);
  console.log(`📡 客户端连接地址: ws://localhost:${CONFIG.PORT}`);
  console.log(`📊 状态页面: http://localhost:${CONFIG.PORT}/status`);
  console.log(`💚 健康检查: http://localhost:${CONFIG.PORT}/health`);
  console.log(`🔧 连接管理 API:`);
  console.log(`   - POST /api/connect - 连接到目标服务器`);
  console.log(`   - POST /api/disconnect - 断开连接`);
  console.log(`   - GET /api/config - 获取配置`);
  console.log(`   - PUT /api/config - 更新配置`);

  if (CONFIG.AUTO_CONNECT) {
    console.log(`\n🔄 自动连接模式已启用`);
    if (CONFIG.SIGNALR_MODE) {
      console.log(`🎯 SignalR 配置:`);
      console.log(`   - SignalR URL: ${CONFIG.SIGNALR_URL}`);
      console.log(`   - Hub: ${CONFIG.SIGNALR_HUB}`);
      console.log(`   - Protocol: ${CONFIG.SIGNALR_PROTOCOL}`);

      // 创建默认 SignalR 目标连接
      createTargetConnection({
        name: 'Default SignalR',
        type: CONNECTION_TYPES.SIGNALR,
        signalr: {
          url: CONFIG.SIGNALR_URL,
          hub: CONFIG.SIGNALR_HUB,
          protocol: CONFIG.SIGNALR_PROTOCOL
        }
      }).catch(error => {
        console.error(`❌ 自动创建 SignalR 连接失败: ${error.message}`);
      });
    } else {
      console.log(`🎯 WebSocket 配置:`);
      console.log(`   - 目标 URL: ${CONFIG.TARGET_WS_URL}`);

      // 创建默认 WebSocket 目标连接
      createTargetConnection({
        name: 'Default WebSocket',
        type: CONNECTION_TYPES.WEBSOCKET,
        url: CONFIG.TARGET_WS_URL
      }).catch(error => {
        console.error(`❌ 自动创建 WebSocket 连接失败: ${error.message}`);
      });
    }
  } else {
    console.log(`\n⏸️  自动连接已禁用，请使用 API 手动创建目标连接`);
  }

  // 显示配置信息
  console.log(`\n⚙️  服务器配置:`);
  console.log(`   自动连接: ${CONFIG.AUTO_CONNECT ? '启用' : '禁用'}`);
  console.log(`   自动转发: ${CONFIG.AUTO_FORWARD ? '启用' : '禁用'}`);
  console.log(`   SignalR 模式: ${CONFIG.SIGNALR_MODE ? '启用' : '禁用'}`);
  console.log(`   环境变量 AUTO_FORWARD: ${process.env.AUTO_FORWARD || '未设置'}`);

  if (CONFIG.AUTO_FORWARD) {
    console.log(`\n🔄 自动转发模式已启用，所有非控制消息将自动转发到目标服务器`);
  } else {
    console.log(`\n📨 默认转发模式，使用 {"type":"forward_to_target","data":"..."} 格式手动转发消息`);
  }
});

// 优雅关闭
function gracefulShutdown(signal) {
  console.log(`收到 ${signal} 信号，正在关闭服务器...`);

  // 清除重连定时器（兼容旧代码）
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }

  // 关闭所有目标连接
  console.log(`正在关闭 ${targetConnections.size} 个目标连接...`);
  targetConnections.forEach((connectionInfo, id) => {
    if (connectionInfo.ws && connectionInfo.ws.readyState === WebSocket.OPEN) {
      console.log(`关闭目标连接 #${id}: ${connectionInfo.name}`);
      connectionInfo.ws.close();
    }
  });

  // 关闭旧的目标连接（兼容旧代码）
  if (targetConnection && targetConnection.readyState === WebSocket.OPEN) {
    console.log('正在关闭旧的目标服务器连接...');
    targetConnection.close();
  }

  // 通知所有客户端服务器即将关闭
  console.log(`正在通知 ${clients.size} 个客户端...`);
  broadcastToClients(JSON.stringify({
    type: 'system',
    message: '服务器即将关闭'
  }));

  // 等待一秒让消息发送完成，然后关闭所有客户端连接
  setTimeout(() => {
    clients.forEach((client, id) => {
      if (client.ws.readyState === WebSocket.OPEN) {
        client.ws.close(1001, '服务器关闭');
      }
    });
    clients.clear();

    // 关闭 HTTP 服务器
    server.close(() => {
      console.log('服务器已关闭');
      process.exit(0);
    });
  }, 1000);
}

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
