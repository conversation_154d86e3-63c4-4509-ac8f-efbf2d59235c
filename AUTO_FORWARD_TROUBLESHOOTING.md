# 自动转发功能故障排除指南

## 🔍 问题诊断

如果你看到消息"已收到消息，但未启用自动转发"，请按照以下步骤进行诊断：

### 1. 检查服务器配置

运行配置检查脚本：
```bash
npm run test:config
```

或者手动检查：
```bash
curl http://localhost:8080/api/config
```

查看返回的配置信息中的 `autoForward` 字段。

### 2. 检查服务器启动日志

正确启动自动转发模式时，你应该看到：
```
⚙️  服务器配置:
   自动连接: 禁用
   自动转发: 启用
   SignalR 模式: 禁用
   环境变量 AUTO_FORWARD: true

🔄 自动转发模式已启用，所有非控制消息将自动转发到目标服务器
```

### 3. 检查客户端连接

确保客户端连接到了目标服务器：
```
ws://localhost:8080?target=1
```

其中 `target=1` 是你创建的目标连接 ID。

## 🛠️ 解决方案

### 方案 1: 使用专用启动脚本

```bash
npm run start:auto-forward
```

这个脚本会自动设置正确的环境变量。

### 方案 2: 手动设置环境变量

**Linux/macOS:**
```bash
AUTO_FORWARD=true AUTO_CONNECT=false node server.js
```

**Windows (CMD):**
```cmd
set AUTO_FORWARD=true && set AUTO_CONNECT=false && node server.js
```

**Windows (PowerShell):**
```powershell
$env:AUTO_FORWARD="true"; $env:AUTO_CONNECT="false"; node server.js
```

### 方案 3: 使用专用启动文件

```bash
node start-auto-forward.js
```

## 🧪 测试自动转发

### 快速测试
```bash
npm run test:quick
```

### 完整测试
```bash
npm run test:auto
```

## 📋 完整的测试流程

1. **启动自动转发服务器**
   ```bash
   npm run start:auto-forward
   ```

2. **验证配置**
   ```bash
   curl http://localhost:8080/api/config | grep autoForward
   ```

3. **创建目标连接**
   ```bash
   curl -X POST http://localhost:8080/api/targets \
     -H "Content-Type: application/json" \
     -d '{"name": "Test Echo", "url": "ws://echo.websocket.org"}'
   ```

4. **连接客户端并测试**
   ```javascript
   const ws = new WebSocket('ws://localhost:8080?target=1');
   
   ws.onopen = () => {
     // 发送 Phoenix Channel 消息
     ws.send('["6","6","driver_radio_transcriptions:session:9921","phx_join",{}]');
   };
   
   ws.onmessage = (event) => {
     const data = JSON.parse(event.data);
     if (data.type === 'auto_forward_response') {
       console.log('自动转发成功:', data.message);
     }
   };
   ```

## 🔧 调试信息

启用自动转发后，服务器会显示详细的调试信息：

```
🔍 自动转发检查: process.env.AUTO_FORWARD=true, CONFIG.AUTO_FORWARD=true, autoForward=true
🔍 客户端目标连接: clientInfo.targetConnectionId=1
🔄 自动转发模式：转发普通消息到目标服务器
```

如果看到：
```
❌ 不转发消息，原因: 自动转发未启用
```

说明配置有问题，请检查环境变量设置。

## 🚨 常见问题

### 问题 1: 环境变量未生效
**症状**: 启动时显示"自动转发: 禁用"
**解决**: 使用 `npm run start:auto-forward` 或 `node start-auto-forward.js`

### 问题 2: 客户端未连接到目标
**症状**: 显示"客户端未连接到目标服务器"
**解决**: 确保 WebSocket URL 包含 `?target=目标ID`

### 问题 3: 目标连接不存在
**症状**: 转发失败
**解决**: 先创建目标连接，等待连接建立后再测试

### 问题 4: 消息格式问题
**症状**: 消息被识别为控制消息
**解决**: 确保消息不是 JSON 格式的控制消息（如 `{"type":"ping"}`）

## 📝 验证清单

- [ ] 服务器启动时显示"自动转发: 启用"
- [ ] 环境变量 `AUTO_FORWARD=true`
- [ ] 客户端连接 URL 包含 `?target=ID`
- [ ] 目标连接已创建且状态为"connected"
- [ ] 发送的消息不是控制消息格式
- [ ] 服务器日志显示"🔄 自动转发模式：转发普通消息到目标服务器"

## 🎯 预期行为

**自动转发启用时:**
```
📨 客户端 #1 请求转发消息: ...
🔄 转发消息到目标服务器: ...
✅ 消息已成功转发到目标服务器
📤 向客户端 #1 发送转发响应: 成功
```

**自动转发禁用时:**
```
❌ 不转发消息，原因: 自动转发未启用
```

按照这个指南，你应该能够成功启用和使用自动转发功能！
