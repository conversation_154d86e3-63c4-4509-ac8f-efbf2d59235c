const WebSocket = require('ws');

console.log('🧹 测试清理后的配置功能\n');

// 简单的 HTTP 请求函数
function httpRequest(url, method = 'GET', data = null) {
  const http = require('http');
  const urlParts = new URL(url);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testCleanConfig() {
  console.log('🔍 检查服务器配置...\n');
  
  try {
    // 1. 检查配置 API
    console.log('1. 测试配置 API...');
    const configResponse = await httpRequest('http://localhost:8080/api/config');
    
    if (configResponse.status === 200) {
      console.log('✅ 配置 API 访问成功');
      const config = configResponse.data;
      
      // 检查是否还有 autoForward 字段
      if (config.hasOwnProperty('autoForward')) {
        console.log('❌ 配置中仍包含 autoForward 字段');
        console.log(`   autoForward: ${config.autoForward}`);
      } else {
        console.log('✅ 配置中已移除 autoForward 字段');
      }
      
      // 检查环境变量
      if (config.environment && config.environment.hasOwnProperty('AUTO_FORWARD')) {
        console.log('❌ 环境变量中仍包含 AUTO_FORWARD');
        console.log(`   AUTO_FORWARD: ${config.environment.AUTO_FORWARD}`);
      } else {
        console.log('✅ 环境变量中已移除 AUTO_FORWARD');
      }
      
      console.log('\n📋 当前配置:');
      console.log(`   模式: ${config.mode}`);
      console.log(`   自动连接: ${config.autoConnect}`);
      console.log(`   SignalR 模式: ${config.signalrMode}`);
      
    } else {
      console.log(`❌ 配置 API 访问失败，状态码: ${configResponse.status}`);
    }
    
    // 2. 测试配置更新 API
    console.log('\n2. 测试配置更新 API...');
    
    // 尝试更新 autoForward（应该被忽略）
    const updateResponse = await httpRequest('http://localhost:8080/api/config', 'PUT', {
      autoForward: true,
      autoReconnect: true
    });
    
    if (updateResponse.status === 200) {
      console.log('✅ 配置更新 API 正常工作');
      const updateResult = updateResponse.data;
      
      if (updateResult.config && updateResult.config.hasOwnProperty('autoForward')) {
        console.log('❌ 更新响应中仍包含 autoForward 字段');
      } else {
        console.log('✅ 更新响应中已移除 autoForward 字段');
      }
      
      console.log('📋 更新后的配置:');
      console.log(JSON.stringify(updateResult.config, null, 2));
      
    } else {
      console.log(`❌ 配置更新失败，状态码: ${updateResponse.status}`);
    }
    
    // 3. 测试基于消息字段的转发控制
    console.log('\n3. 测试基于消息字段的转发控制...');
    
    // 创建测试目标
    const targetResponse = await httpRequest('http://localhost:8080/api/targets', 'POST', {
      name: 'Clean Config Test',
      url: 'ws://echo.websocket.org'
    });
    
    if (targetResponse.status === 200 && targetResponse.data.success) {
      const targetId = targetResponse.data.connection.id;
      console.log(`✅ 测试目标创建成功，ID: ${targetId}`);
      
      // 等待连接建立
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 测试消息转发
      await testMessageForwarding(targetId);
      
      // 清理测试目标
      await httpRequest(`http://localhost:8080/api/targets/${targetId}`, 'DELETE');
      console.log('✅ 测试目标已清理');
      
    } else {
      console.log('❌ 无法创建测试目标');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

async function testMessageForwarding(targetId) {
  return new Promise((resolve) => {
    const wsUrl = `ws://localhost:8080?target=${targetId}`;
    const ws = new WebSocket(wsUrl);
    
    let testResults = {
      forwardTrueReceived: false,
      forwardFalseReceived: false,
      noForwardReceived: false
    };
    
    const timeout = setTimeout(() => {
      console.log('⏰ 消息转发测试超时');
      ws.close();
      resolve(testResults);
    }, 15000);
    
    ws.on('open', () => {
      console.log('   ✅ WebSocket 连接建立');
      
      // 测试 1: forward: true（应该转发）
      setTimeout(() => {
        console.log('   📤 发送 forward: true 消息');
        ws.send(JSON.stringify({
          forward: true,
          data: "test message with forward true"
        }));
      }, 1000);
      
      // 测试 2: forward: false（不应该转发）
      setTimeout(() => {
        console.log('   📤 发送 forward: false 消息');
        ws.send(JSON.stringify({
          forward: false,
          data: "test message with forward false"
        }));
      }, 3000);
      
      // 测试 3: 没有 forward 字段（不应该转发）
      setTimeout(() => {
        console.log('   📤 发送没有 forward 字段的消息');
        ws.send(JSON.stringify({
          data: "test message without forward field"
        }));
      }, 5000);
      
      // 关闭连接
      setTimeout(() => {
        ws.close();
      }, 8000);
    });
    
    ws.on('message', (data) => {
      const message = data.toString();
      console.log(`   📥 收到响应: ${message.substring(0, 100)}...`);
      
      try {
        const parsed = JSON.parse(message);
        
        switch (parsed.type) {
          case 'auto_forward_response':
            if (parsed.success) {
              console.log('   ✅ 消息转发成功');
              testResults.forwardTrueReceived = true;
            }
            break;
            
          case 'system':
            if (parsed.reason && parsed.reason.includes('未包含转发指令')) {
              console.log('   ✅ 正确拒绝了没有转发指令的消息');
              testResults.noForwardReceived = true;
            }
            break;
            
          default:
            // Echo 服务器回显
            if (message.includes('forward true')) {
              console.log('   ✅ 收到 Echo 回显 - forward: true 消息正确转发');
            }
        }
      } catch (e) {
        // 原始回显
        if (message.includes('forward true')) {
          console.log('   ✅ 收到原始 Echo 回显');
        }
      }
    });
    
    ws.on('close', () => {
      clearTimeout(timeout);
      console.log('   ❌ WebSocket 连接关闭');
      resolve(testResults);
    });
    
    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.error('   💥 WebSocket 错误:', error.message);
      resolve(testResults);
    });
  });
}

async function main() {
  console.log('🎯 清理后的配置功能测试套件\n');
  console.log('📋 此测试将验证：');
  console.log('   - 配置 API 中已移除 autoForward 字段');
  console.log('   - 环境变量中已移除 AUTO_FORWARD');
  console.log('   - 配置更新 API 不再处理 autoForward');
  console.log('   - 基于消息字段的转发控制正常工作\n');
  
  await testCleanConfig();
  
  console.log('\n📊 测试总结:');
  console.log('   - 全局自动转发配置已完全移除');
  console.log('   - 转发控制现在完全基于消息字段');
  console.log('   - forward: true 的消息会被转发');
  console.log('   - 没有 forward 字段的消息不会被转发');
  console.log('   - 配置 API 已清理干净');
  
  console.log('\n🎉 配置清理验证完成！');
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出测试...');
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  console.log('请确保代理服务器正在运行: npm run start:no-auto\n');
  main();
}
