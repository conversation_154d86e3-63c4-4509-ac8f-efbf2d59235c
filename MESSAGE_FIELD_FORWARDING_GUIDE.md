# 基于消息字段的转发控制指南

## 🎯 功能概述

现在可以通过在每个消息中添加 `forward` 字段来控制是否转发该消息，而不需要全局设置。这提供了更精细的转发控制。

## 🚀 使用方法

### 1. 转发消息 (`forward: true`)

#### 方式 1: 使用 `data` 字段
```javascript
// 发送转发请求
ws.send(JSON.stringify({
  forward: true,
  data: {
    command: "getData",
    params: { id: 123 }
  }
}));

// 实际转发到目标服务器的内容（只有 data 字段的内容）
{
  "command": "getData",
  "params": { "id": 123 }
}
```

#### 方式 2: 直接内容（移除 forward 字段）
```javascript
// 发送转发请求
ws.send(JSON.stringify({
  forward: true,
  command: "processData",
  value: 456,
  timestamp: "2024-01-01T00:00:00Z"
}));

// 实际转发到目标服务器的内容（移除 forward 字段）
{
  "command": "processData",
  "value": 456,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### Phoenix Channel 消息示例
```javascript
// 发送 Phoenix Channel 消息
ws.send(JSON.stringify({
  forward: true,
  data: ["6", "6", "driver_radio_transcriptions:session:9921", "phx_join", {}]
}));

// 转发到目标服务器的内容
["6", "6", "driver_radio_transcriptions:session:9921", "phx_join", {}]
```

### 2. 不转发消息 (`forward: false` 或无 `forward` 字段)

```javascript
// 这些消息不会被转发
ws.send(JSON.stringify({
  forward: false,
  data: "This will not be forwarded"
}));

ws.send(JSON.stringify({
  command: "localCommand",
  data: "This will not be forwarded either"
}));
```

## 📋 转发规则

### 转发条件
1. **消息包含 `forward: true`** - 消息会被转发
2. **消息包含 `forward: false`** - 消息不会被转发
3. **消息没有 `forward` 字段** - 消息不会被转发（除非启用全局自动转发）

### 转发内容规则
1. **有 `data` 字段** - 只转发 `data` 字段的内容
2. **无 `data` 字段** - 转发除 `forward` 字段外的所有内容

### 数据类型支持
- ✅ **JSON 对象**: `{"command": "getData"}`
- ✅ **JSON 数组**: `["6", "6", "channel", "event", {}]`
- ✅ **字符串**: `"Hello World"`
- ✅ **数字**: `123`
- ✅ **布尔值**: `true`, `false`
- ✅ **null**: `null`

## 🔄 响应格式

### 转发成功响应
```json
{
  "type": "auto_forward_response",
  "success": true,
  "message": "消息已转发到目标服务器",
  "targetId": 1,
  "originalMessage": "转发的内容..."
}
```

### 不转发的系统消息
```json
{
  "type": "system",
  "message": "已收到消息，但未转发。要转发消息，请在消息中添加 \"forward\": true 字段",
  "receivedLength": 123,
  "clientId": "client_123",
  "reason": "消息未包含转发指令 (forward: true)",
  "examples": [
    "{\"forward\": true, \"data\": \"your_message\"}",
    "{\"forward\": true, \"command\": \"getData\", \"params\": {...}}",
    "{\"type\": \"forward_to_target\", \"data\": \"your_message\"}"
  ]
}
```

## 🧪 测试示例

### 测试脚本
```bash
# 测试基于消息字段的转发控制
npm run test:field
```

### 手动测试
```javascript
const ws = new WebSocket('ws://localhost:8080?target=1');

ws.onopen = () => {
  // 测试 1: 转发 JSON 对象
  ws.send(JSON.stringify({
    forward: true,
    data: { command: "test", value: 123 }
  }));
  
  // 测试 2: 转发 Phoenix Channel 消息
  ws.send(JSON.stringify({
    forward: true,
    data: ["6", "6", "channel", "phx_join", {}]
  }));
  
  // 测试 3: 不转发消息
  ws.send(JSON.stringify({
    forward: false,
    data: "This will not be forwarded"
  }));
  
  // 测试 4: 没有 forward 字段（不转发）
  ws.send(JSON.stringify({
    command: "localCommand",
    data: "This will not be forwarded"
  }));
};
```

## 🔧 与其他转发方式的对比

| 转发方式 | 语法 | 转发条件 | 转发内容 |
|----------|------|----------|----------|
| **消息字段控制** | `{forward: true, data: "..."}` | 每消息控制 | 纯净数据 |
| 传统转发 | `{type: "forward_to_target", data: "..."}` | 手动指定 | 纯净数据 |
| 全局自动转发 | 普通消息 | 全局开关 | 完整消息 |

## 💡 最佳实践

### 1. 精细控制
```javascript
// 只转发重要的业务消息
ws.send(JSON.stringify({
  forward: true,  // 明确指定转发
  data: {
    event: "user_action",
    payload: { userId: 123, action: "login" }
  }
}));

// 本地调试消息不转发
ws.send(JSON.stringify({
  type: "debug",
  message: "Local debug info"
  // 没有 forward 字段，不会转发
}));
```

### 2. 条件转发
```javascript
function sendMessage(data, shouldForward = false) {
  const message = {
    timestamp: new Date().toISOString(),
    data: data
  };
  
  if (shouldForward) {
    message.forward = true;
  }
  
  ws.send(JSON.stringify(message));
}

// 使用示例
sendMessage({ command: "getData" }, true);   // 会转发
sendMessage({ debug: "local info" }, false); // 不转发
```

### 3. 批量操作
```javascript
const messages = [
  { forward: true, data: { command: "cmd1" } },
  { forward: false, data: { debug: "info" } },
  { forward: true, data: { command: "cmd2" } }
];

messages.forEach(msg => {
  ws.send(JSON.stringify(msg));
});
```

## 🔍 故障排除

### 问题 1: 消息没有被转发
**检查**: 确保消息包含 `forward: true`
```javascript
// ❌ 错误
ws.send(JSON.stringify({ data: "message" }));

// ✅ 正确
ws.send(JSON.stringify({ forward: true, data: "message" }));
```

### 问题 2: 转发的内容不正确
**检查**: 确保数据结构正确
```javascript
// 转发 data 字段的内容
ws.send(JSON.stringify({
  forward: true,
  data: { command: "test" }  // 只有这部分会转发
}));

// 转发除 forward 外的所有内容
ws.send(JSON.stringify({
  forward: true,
  command: "test",  // 这些都会转发
  params: { id: 123 }
}));
```

### 问题 3: 收到系统消息而不是转发响应
**原因**: 消息没有包含转发指令
**解决**: 添加 `forward: true` 字段

## 🎯 使用场景

### 1. 混合应用
```javascript
// 业务消息转发到目标服务器
ws.send(JSON.stringify({
  forward: true,
  data: { event: "business_event", payload: {...} }
}));

// 监控消息只在代理服务器处理
ws.send(JSON.stringify({
  type: "monitoring",
  data: { cpu: 80, memory: 60 }
}));
```

### 2. 开发调试
```javascript
// 生产消息转发
if (isProduction) {
  ws.send(JSON.stringify({
    forward: true,
    data: productionData
  }));
} else {
  // 开发环境不转发
  ws.send(JSON.stringify({
    debug: true,
    data: debugData
  }));
}
```

### 3. 条件路由
```javascript
function sendToTarget(data, condition) {
  ws.send(JSON.stringify({
    forward: condition,  // 根据条件决定是否转发
    data: data,
    metadata: { timestamp: Date.now() }
  }));
}
```

通过基于消息字段的转发控制，你可以实现更灵活、更精细的消息路由策略！
