const WebSocket = require('ws');

// 配置
const PROXY_URL = 'ws://localhost:8080';

console.log('🏎️  F1 SignalR WebSocket 转发测试\n');
console.log('此测试脚本用于测试 F1 实时数据的 SignalR 连接转发');
console.log('请确保代理服务器已启用 SignalR 模式\n');

async function testSignalRProxy() {
  console.log(`连接到代理服务器: ${PROXY_URL}`);
  
  return new Promise((resolve, reject) => {
    const ws = new WebSocket(PROXY_URL);
    let messageCount = 0;
    let systemMessages = 0;
    let dataMessages = 0;
    let startTime;
    
    ws.on('open', () => {
      console.log('✅ 已连接到代理服务器');
      startTime = Date.now();
      
      // SignalR 连接建立后，服务器会自动发送订阅消息
      // 我们也可以发送自定义订阅消息
      setTimeout(() => {
        console.log('📤 发送自定义 SignalR 订阅消息...');
        const customSubscription = {
          H: "Streaming",
          M: "Subscribe",
          A: [["Heartbeat", "CarData.z", "Position.z"]],
          I: 2
        };
        ws.send(JSON.stringify(customSubscription));
      }, 3000);
    });
    
    ws.on('message', (data) => {
      messageCount++;
      const messageStr = data.toString();
      
      // 检查是否是系统消息
      try {
        const parsed = JSON.parse(messageStr);
        if (parsed.type === 'system') {
          systemMessages++;
          console.log(`🔔 系统消息 #${systemMessages}: ${parsed.message}`);
          return;
        }
      } catch (e) {
        // 不是系统消息格式，继续处理
      }
      
      dataMessages++;
      
      // SignalR 消息通常是特定格式的 JSON
      if (messageStr.startsWith('{')) {
        try {
          const signalrData = JSON.parse(messageStr);
          console.log(`📥 SignalR 数据消息 #${dataMessages}:`);
          
          // 检查是否是 SignalR 协议消息
          if (signalrData.C) {
            console.log(`   - 消息ID: ${signalrData.C}`);
          }
          if (signalrData.M) {
            console.log(`   - 消息内容: ${JSON.stringify(signalrData.M).substring(0, 100)}...`);
          }
          if (signalrData.H) {
            console.log(`   - Hub: ${signalrData.H}`);
          }
          if (signalrData.S) {
            console.log(`   - 状态: ${signalrData.S}`);
          }
          
        } catch (e) {
          console.log(`📥 数据消息 #${dataMessages}: ${messageStr.substring(0, 100)}...`);
        }
      } else {
        console.log(`📥 数据消息 #${dataMessages}: ${messageStr.substring(0, 100)}...`);
      }
    });
    
    ws.on('close', (code, reason) => {
      const duration = Date.now() - startTime;
      console.log(`\n❌ 连接已关闭 (${code}: ${reason || '无'})`);
      console.log(`\n📊 测试统计:`);
      console.log(`   - 连接持续时间: ${duration}ms`);
      console.log(`   - 总消息数: ${messageCount}`);
      console.log(`   - 系统消息数: ${systemMessages}`);
      console.log(`   - 数据消息数: ${dataMessages}`);
      
      resolve();
    });
    
    ws.on('error', (error) => {
      console.error(`💥 WebSocket 错误: ${error.message}`);
      reject(error);
    });
    
    // 30秒后主动关闭连接
    setTimeout(() => {
      console.log('\n⏰ 测试时间到，关闭连接...');
      ws.close();
    }, 30000);
  });
}

async function testSubscriptionAPI() {
  console.log('🔍 测试 SignalR 订阅 API...\n');

  try {
    const response = await fetch('http://localhost:8080/signalr/subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();

    if (response.ok) {
      console.log('✅ 订阅 API 测试成功:', result.message);
    } else {
      console.log('⚠️  订阅 API 响应:', result.error || result.message);
    }

  } catch (error) {
    console.log('❌ 订阅 API 测试失败:', error.message);
  }
}

async function checkProxyStatus() {
  console.log('🔍 检查代理服务器状态...\n');

  try {
    const response = await fetch('http://localhost:8080/');
    const info = await response.json();
    
    console.log('📋 代理服务器信息:');
    console.log(`   - 名称: ${info.name}`);
    console.log(`   - 模式: ${info.mode || 'WebSocket'}`);
    
    if (info.mode === 'SignalR') {
      console.log(`   - SignalR URL: ${info.signalrUrl}`);
      console.log(`   - Hub: ${info.signalrHub}`);
      console.log(`   - 协议版本: ${info.signalrProtocol}`);
      console.log(`   - 连接令牌: ${info.hasConnectionToken ? '已获取' : '未获取'}`);
    } else {
      console.log(`   - 目标 URL: ${info.targetUrl}`);
    }
    
    console.log(`   - 目标连接状态: ${info.targetConnection}`);
    console.log(`   - 客户端连接数: ${info.clientConnections}`);
    
    if (info.mode !== 'SignalR') {
      console.log('\n⚠️  警告: 代理服务器未启用 SignalR 模式');
      console.log('请设置环境变量 SIGNALR_MODE=true 并重启服务器');
      return false;
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ 无法连接到代理服务器:', error.message);
    console.log('请确保代理服务器正在运行在 http://localhost:8080');
    return false;
  }
}

async function main() {
  try {
    console.log('🎯 SignalR WebSocket 转发测试套件\n');
    
    // 检查代理服务器状态
    const isReady = await checkProxyStatus();
    if (!isReady) {
      process.exit(1);
    }
    
    console.log('\n✅ 代理服务器状态正常，开始测试...\n');

    // 测试订阅 API
    await testSubscriptionAPI();

    // 等待用户确认
    console.log('\n按 Enter 键开始 WebSocket 测试...');
    process.stdin.setRawMode(true);
    process.stdin.resume();
    await new Promise(resolve => {
      process.stdin.once('data', () => {
        process.stdin.setRawMode(false);
        resolve();
      });
    });

    // 测试 SignalR 连接
    await testSignalRProxy();
    
    console.log('\n🎉 测试完成！');
    process.exit(0);
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message);
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('💥 未捕获的异常:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('💥 未处理的 Promise 拒绝:', reason);
  process.exit(1);
});

// 运行测试
if (require.main === module) {
  main();
}
