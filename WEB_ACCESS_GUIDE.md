# Web 界面访问指南

## 🌐 正确的访问方式

### ✅ 推荐方式：通过 HTTP 服务器访问

1. **启动服务器**
   ```bash
   npm run start:no-auto
   ```

2. **在浏览器中访问**
   ```
   http://localhost:8080
   ```

3. **自动配置**
   - API 基础 URL 会自动设置为当前服务器地址
   - 所有功能正常工作，包括自动转发模式切换

### ❌ 不推荐：直接打开 HTML 文件

```
file:///path/to/test-client.html  ❌ 会导致 CORS 错误
```

## 🔧 功能对比

| 访问方式 | API 调用 | 自动转发切换 | WebSocket 连接 | 推荐度 |
|----------|----------|--------------|----------------|--------|
| HTTP 服务器 | ✅ 正常 | ✅ 正常 | ✅ 正常 | ⭐⭐⭐⭐⭐ |
| 直接打开文件 | ❌ CORS 错误 | ❌ 无法切换 | ✅ 正常 | ⭐ |

## 🚀 完整使用流程

### 1. 启动服务器
```bash
# 方式 1: 默认模式
npm run start:no-auto

# 方式 2: 自动转发模式
npm run start:auto-forward

# 方式 3: 手动设置
AUTO_FORWARD=true npm run start:no-auto
```

### 2. 访问 Web 界面
```
http://localhost:8080
```

### 3. 验证功能
- ✅ API 基础 URL 自动设置为 `http://localhost:8080`
- ✅ 自动转发模式切换正常工作
- ✅ 所有 API 调试功能可用

## 🔍 故障排除

### 问题 1: CORS 错误
```
Access to fetch at 'file:///api/config' from origin 'null' has been blocked by CORS policy
```

**解决方案**: 不要直接打开 HTML 文件，使用 HTTP 服务器访问：
```
http://localhost:8080  ✅
```

### 问题 2: API 调用失败
```
PUT file:///api/config net::ERR_FAILED
```

**解决方案**: 确保通过 HTTP 访问，并且 API 基础 URL 正确设置。

### 问题 3: 自动转发切换无效

**检查步骤**:
1. 确保通过 `http://localhost:8080` 访问
2. 检查浏览器控制台是否有错误
3. 使用"测试自动转发配置"按钮验证状态

## 📋 功能验证清单

访问 `http://localhost:8080` 后，验证以下功能：

- [ ] 页面正常加载
- [ ] API 基础 URL 自动设置为 `http://localhost:8080`
- [ ] 可以刷新目标连接列表
- [ ] 可以创建/删除目标连接
- [ ] 自动转发模式复选框可以切换
- [ ] "测试自动转发配置"按钮正常工作
- [ ] WebSocket 连接功能正常
- [ ] 消息转发功能正常

## 💡 开发提示

### 本地开发
```bash
# 启动开发服务器
npm run dev

# 访问 Web 界面
http://localhost:8080
```

### 生产部署
```bash
# 设置环境变量
export AUTO_FORWARD=true
export PORT=3000

# 启动服务器
npm run start:no-auto

# 访问 Web 界面
http://your-server:3000
```

### 自定义端口
```bash
# 使用自定义端口
PORT=9000 npm run start:no-auto

# 访问 Web 界面
http://localhost:9000
```

## 🎯 最佳实践

1. **始终通过 HTTP 访问** Web 界面
2. **使用服务器提供的静态文件服务**
3. **让 API 基础 URL 自动配置**
4. **使用浏览器开发者工具调试**

## 📊 服务器启动信息

正确启动后，你会看到：

```
🚀 WebSocket 转发代理服务器 (一对多模式) 启动在端口 8080
🌐 Web 测试客户端: http://localhost:8080
📡 WebSocket 连接地址: ws://localhost:8080
📊 状态页面: http://localhost:8080/status
💚 健康检查: http://localhost:8080/health
🔧 API 端点:
   - GET /api - API 信息
   - GET /api/targets - 获取目标连接列表
   - POST /api/targets - 创建目标连接
   - DELETE /api/targets/:id - 删除目标连接
   - GET /api/config - 获取配置
   - PUT /api/config - 更新配置

⚙️  服务器配置:
   自动连接: 禁用
   自动转发: 禁用/启用
   SignalR 模式: 禁用
   环境变量 AUTO_FORWARD: undefined/true
```

点击 `🌐 Web 测试客户端: http://localhost:8080` 链接即可访问完整功能的 Web 界面！
