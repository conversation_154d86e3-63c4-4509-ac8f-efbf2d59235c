# 消息转发功能使用指南

## 🎯 功能概述

WebSocket 代理服务器现在支持两种消息处理模式：

1. **心跳和控制消息** - 用于客户端与代理服务器之间的通信（不转发）
2. **转发消息** - 用于与目标服务器进行数据交互（转发到目标服务器）

## 📨 消息类型详解

### 1. 心跳和控制消息（不转发）

这些消息用于维护连接和获取状态信息，不会转发到目标服务器：

#### Ping 消息
```javascript
ws.send(JSON.stringify({ type: "ping" }));

// 响应
{
  "type": "pong",
  "timestamp": "2025-07-27T16:08:49.123Z"
}
```

#### 状态查询
```javascript
ws.send(JSON.stringify({ type: "status" }));

// 响应
{
  "type": "status_response",
  "clientId": 1,
  "mode": "Multi-Target",
  "totalTargets": 2,
  "connectedTargets": 1,
  "totalClients": 3,
  "availableTargets": [...]
}
```

#### SignalR 订阅
```javascript
ws.send(JSON.stringify({ type: "signalr_subscribe" }));

// 响应
{
  "type": "signalr_subscribe_response",
  "success": true,
  "message": "SignalR 订阅已发送",
  "targetId": 1
}
```

### 2. 转发消息到目标服务器

这些消息会被转发到客户端连接的目标服务器：

#### 转发文本消息
```javascript
ws.send(JSON.stringify({
  type: "forward_to_target",
  data: "Hello Target Server!"
}));

// 响应
{
  "type": "forward_response",
  "success": true,
  "message": "消息已转发到目标服务器",
  "targetId": 1,
  "originalMessage": "Hello Target Server!"
}
```

#### 转发 JSON 数据
```javascript
ws.send(JSON.stringify({
  type: "forward_to_target",
  data: {
    command: "getData",
    params: { id: 123, type: "user" }
  }
}));

// 响应
{
  "type": "forward_response",
  "success": true,
  "message": "消息已转发到目标服务器",
  "targetId": 1,
  "originalMessage": {...}
}
```

#### 转发失败的情况
```javascript
// 客户端未连接到目标服务器时
{
  "type": "forward_response",
  "success": false,
  "message": "客户端未连接到目标服务器",
  "originalMessage": "..."
}
```

## 🚀 使用示例

### 基本转发示例

```javascript
const ws = new WebSocket('ws://localhost:8080?target=1');

ws.onopen = () => {
  console.log('连接已建立');
  
  // 发送心跳（不转发）
  ws.send(JSON.stringify({ type: "ping" }));
  
  // 转发消息到目标服务器
  ws.send(JSON.stringify({
    type: "forward_to_target",
    data: "Hello from client!"
  }));
};

ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  
  switch (message.type) {
    case 'pong':
      console.log('收到心跳响应');
      break;
      
    case 'forward_response':
      console.log('转发响应:', message.message);
      break;
      
    case 'system':
      console.log('系统消息:', message.message);
      break;
      
    default:
      console.log('收到数据:', event.data);
  }
};
```

### Echo 服务器交互示例

```javascript
const ws = new WebSocket('ws://localhost:8080?target=1'); // 连接到 Echo 服务器

ws.onopen = () => {
  // 转发消息到 Echo 服务器
  ws.send(JSON.stringify({
    type: "forward_to_target",
    data: "Echo test message"
  }));
};

ws.onmessage = (event) => {
  const data = event.data;
  
  try {
    const parsed = JSON.parse(data);
    if (parsed.type === 'forward_response') {
      console.log('转发状态:', parsed.message);
      return;
    }
  } catch (e) {
    // 可能是来自 Echo 服务器的回显
    if (data.includes('Echo test message')) {
      console.log('收到 Echo 服务器回显:', data);
    }
  }
};
```

### SignalR 服务器交互示例

```javascript
const ws = new WebSocket('ws://localhost:8080?target=2'); // 连接到 SignalR 服务器

ws.onopen = () => {
  // 触发 SignalR 订阅
  ws.send(JSON.stringify({ type: "signalr_subscribe" }));
  
  // 发送自定义命令到 SignalR 服务器
  ws.send(JSON.stringify({
    type: "forward_to_target",
    data: {
      H: "MyHub",
      M: "SendMessage",
      A: ["Hello SignalR!"]
    }
  }));
};
```

## 🧪 测试转发功能

### 自动化测试
```bash
npm run test:forward
```

### Web 界面测试
1. 打开 `test-client.html`
2. 创建目标连接（如 Echo Server）
3. 连接到目标
4. 使用"转发消息到目标服务器"功能

### 手动测试步骤

1. **启动代理服务器**
   ```bash
   npm run start:no-auto
   ```

2. **创建 Echo 目标连接**
   ```bash
   curl -X POST http://localhost:8080/api/targets \
     -H "Content-Type: application/json" \
     -d '{"name": "Echo Server", "url": "ws://echo.websocket.org"}'
   ```

3. **连接客户端并测试转发**
   ```javascript
   const ws = new WebSocket('ws://localhost:8080?target=1');
   
   ws.onopen = () => {
     // 测试转发
     ws.send(JSON.stringify({
       type: "forward_to_target",
       data: "Test forwarding!"
     }));
   };
   ```

## 🔧 错误处理

### 常见错误和解决方案

#### 1. 转发失败 - 未连接到目标
```json
{
  "type": "forward_response",
  "success": false,
  "message": "客户端未连接到目标服务器"
}
```
**解决方案**: 确保客户端连接时指定了有效的目标 ID

#### 2. 转发失败 - 目标连接断开
```json
{
  "type": "forward_response",
  "success": false,
  "message": "消息转发失败"
}
```
**解决方案**: 检查目标服务器状态，可能需要重新创建目标连接

#### 3. 不支持的消息类型
```json
{
  "type": "error",
  "message": "不支持的消息类型",
  "receivedType": "unknown_type",
  "supportedTypes": ["ping", "status", "signalr_subscribe", "forward_to_target"]
}
```
**解决方案**: 使用支持的消息类型

## 💡 最佳实践

### 1. 消息分类
- 使用心跳消息维护连接活跃状态
- 使用状态查询获取服务器信息
- 使用转发消息与目标服务器交互

### 2. 错误处理
- 始终检查转发响应的 `success` 字段
- 处理连接断开和重连场景
- 实现消息重试机制

### 3. 性能优化
- 避免频繁发送大量转发消息
- 合理使用心跳间隔
- 监控目标服务器响应时间

### 4. 安全考虑
- 验证转发的消息内容
- 避免转发敏感信息
- 使用 API 密钥保护管理功能

## 🔗 相关文档

- [多目标连接指南](MULTI_TARGET_GUIDE.md)
- [测试客户端指南](TEST_CLIENT_GUIDE.md)
- [安全部署指南](SECURITY_GUIDE.md)

通过消息转发功能，你可以实现客户端与目标服务器之间的双向通信，同时保持代理服务器的控制和监控能力！
