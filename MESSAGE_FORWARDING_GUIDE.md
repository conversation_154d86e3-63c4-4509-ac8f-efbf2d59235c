# 消息转发功能使用指南

## 🎯 功能概述

WebSocket 代理服务器现在支持灵活的消息处理模式：

### 消息类型
1. **心跳和控制消息** - 用于客户端与代理服务器之间的通信（不转发）
2. **转发消息** - 用于与目标服务器进行数据交互（转发到目标服务器）

### 转发模式
1. **默认模式** - 只有明确标记为 `forward_to_target` 的消息才会转发
2. **自动转发模式** - 所有非控制消息都会自动转发到目标服务器

## 📨 消息类型详解

### 1. 心跳和控制消息（不转发）

这些消息用于维护连接和获取状态信息，不会转发到目标服务器：

#### Ping 消息
```javascript
ws.send(JSON.stringify({ type: "ping" }));

// 响应
{
  "type": "pong",
  "timestamp": "2025-07-27T16:08:49.123Z"
}
```

#### 状态查询
```javascript
ws.send(JSON.stringify({ type: "status" }));

// 响应
{
  "type": "status_response",
  "clientId": 1,
  "mode": "Multi-Target",
  "totalTargets": 2,
  "connectedTargets": 1,
  "totalClients": 3,
  "availableTargets": [...]
}
```

#### SignalR 订阅
```javascript
ws.send(JSON.stringify({ type: "signalr_subscribe" }));

// 响应
{
  "type": "signalr_subscribe_response",
  "success": true,
  "message": "SignalR 订阅已发送",
  "targetId": 1
}
```

### 2. 转发消息到目标服务器

#### 手动转发模式（默认）

这些消息会被转发到客户端连接的目标服务器：

#### 转发文本消息
```javascript
ws.send(JSON.stringify({
  type: "forward_to_target",
  data: "Hello Target Server!"
}));

// 响应
{
  "type": "forward_response",
  "success": true,
  "message": "消息已转发到目标服务器",
  "targetId": 1,
  "originalMessage": "Hello Target Server!"
}
```

#### 转发 JSON 数据
```javascript
// 发送转发请求
ws.send(JSON.stringify({
  type: "forward_to_target",
  data: {
    command: "getData",
    params: { id: 123, type: "user" }
  }
}));

// 实际转发到目标服务器的纯净数据（不包含 type 和包装字段）
{
  "command": "getData",
  "params": { "id": 123, "type": "user" }
}

// 客户端收到的响应
{
  "type": "forward_response",
  "success": true,
  "message": "消息已转发到目标服务器",
  "targetId": 1,
  "originalMessage": {...}
}
```

#### 转发失败的情况
```javascript
// 客户端未连接到目标服务器时
{
  "type": "forward_response",
  "success": false,
  "message": "客户端未连接到目标服务器",
  "originalMessage": "..."
}
```

#### 自动转发模式

启用自动转发模式后，所有非控制消息都会自动转发到目标服务器：

```javascript
// 启动自动转发模式
// AUTO_FORWARD=true node server.js
// 或者
// npm run start:auto-forward

// 直接发送消息（会自动转发）
ws.send('["6","6","driver_radio_transcriptions:session:9921","phx_join",{}]');

// 自动转发响应
{
  "type": "auto_forward_response",
  "success": true,
  "message": "消息已自动转发到目标服务器",
  "targetId": 1,
  "originalMessage": "..."
}
```

#### 默认模式提示

在默认模式下，普通消息不会自动转发，系统会提供转发提示：

```javascript
// 发送普通消息
ws.send('["test","message"]');

// 系统响应
{
  "type": "system",
  "message": "已收到消息，但未启用自动转发。使用 {\"type\":\"forward_to_target\",\"data\":\"your_message\"} 格式来转发消息",
  "hint": "发送 {\"type\":\"forward_to_target\",\"data\":[\"test\",\"message\"]} 来转发此消息"
}
```

## 🧪 纯净数据转发

### 重要特性：只转发原始数据

代理服务器确保转发到目标服务器的数据是**纯净的原始数据**，不包含项目的包装字段。

#### 转发过程说明

```javascript
// 1. 客户端发送转发请求（包含项目包装字段）
{
  "type": "forward_to_target",  // 项目包装字段，不会转发
  "data": ["6", "6", "driver_radio_transcriptions:session:9921", "phx_join", {}]  // 只有这部分会转发
}

// 2. 代理服务器提取 data 字段，转发纯净数据到目标服务器
["6", "6", "driver_radio_transcriptions:session:9921", "phx_join", {}]

// 3. 目标服务器收到的是完全纯净的数据，无任何包装字段
```

#### 支持的数据类型

- **JSON 对象**: `{"command": "getData", "params": {...}}`
- **JSON 数组**: `["6", "6", "channel", "event", {}]`
- **文本字符串**: `"Hello World"`
- **数字和布尔值**: `123`, `true`, `false`
- **null 值**: `null`

#### 验证纯净转发

```bash
# 运行纯净数据转发测试
npm run test:pure
```

## 🚀 使用示例

### 基本转发示例

```javascript
const ws = new WebSocket('ws://localhost:8080?target=1');

ws.onopen = () => {
  console.log('连接已建立');
  
  // 发送心跳（不转发）
  ws.send(JSON.stringify({ type: "ping" }));
  
  // 转发消息到目标服务器
  ws.send(JSON.stringify({
    type: "forward_to_target",
    data: "Hello from client!"
  }));
};

ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  
  switch (message.type) {
    case 'pong':
      console.log('收到心跳响应');
      break;
      
    case 'forward_response':
      console.log('转发响应:', message.message);
      break;
      
    case 'system':
      console.log('系统消息:', message.message);
      break;
      
    default:
      console.log('收到数据:', event.data);
  }
};
```

### Echo 服务器交互示例

```javascript
const ws = new WebSocket('ws://localhost:8080?target=1'); // 连接到 Echo 服务器

ws.onopen = () => {
  // 转发消息到 Echo 服务器
  ws.send(JSON.stringify({
    type: "forward_to_target",
    data: "Echo test message"
  }));
};

ws.onmessage = (event) => {
  const data = event.data;
  
  try {
    const parsed = JSON.parse(data);
    if (parsed.type === 'forward_response') {
      console.log('转发状态:', parsed.message);
      return;
    }
  } catch (e) {
    // 可能是来自 Echo 服务器的回显
    if (data.includes('Echo test message')) {
      console.log('收到 Echo 服务器回显:', data);
    }
  }
};
```

### SignalR 服务器交互示例

```javascript
const ws = new WebSocket('ws://localhost:8080?target=2'); // 连接到 SignalR 服务器

ws.onopen = () => {
  // 触发 SignalR 订阅
  ws.send(JSON.stringify({ type: "signalr_subscribe" }));
  
  // 发送自定义命令到 SignalR 服务器
  ws.send(JSON.stringify({
    type: "forward_to_target",
    data: {
      H: "MyHub",
      M: "SendMessage",
      A: ["Hello SignalR!"]
    }
  }));
};
```

## 🧪 测试转发功能

### 自动化测试
```bash
npm run test:forward      # 基本转发功能测试
npm run test:json         # JSON 数据转发测试
npm run test:pure         # 纯净数据转发测试
npm run test:debug        # 调试日志功能测试
```

### Web 界面测试
1. 打开 `test-client.html`
2. 创建目标连接（如 Echo Server）
3. 连接到目标
4. 使用"转发消息到目标服务器"功能
5. 使用"转发 JSON 数据"功能和模板

### 手动测试步骤

1. **启动代理服务器**
   ```bash
   npm run start:no-auto
   ```

2. **创建 Echo 目标连接**
   ```bash
   curl -X POST http://localhost:8080/api/targets \
     -H "Content-Type: application/json" \
     -d '{"name": "Echo Server", "url": "ws://echo.websocket.org"}'
   ```

3. **连接客户端并测试转发**
   ```javascript
   const ws = new WebSocket('ws://localhost:8080?target=1');
   
   ws.onopen = () => {
     // 测试转发
     ws.send(JSON.stringify({
       type: "forward_to_target",
       data: "Test forwarding!"
     }));
   };
   ```

## 🔧 错误处理

### 常见错误和解决方案

#### 1. 转发失败 - 未连接到目标
```json
{
  "type": "forward_response",
  "success": false,
  "message": "客户端未连接到目标服务器"
}
```
**解决方案**: 确保客户端连接时指定了有效的目标 ID

#### 2. 转发失败 - 目标连接断开
```json
{
  "type": "forward_response",
  "success": false,
  "message": "消息转发失败"
}
```
**解决方案**: 检查目标服务器状态，可能需要重新创建目标连接

#### 3. 不支持的消息类型
```json
{
  "type": "error",
  "message": "不支持的消息类型",
  "receivedType": "unknown_type",
  "supportedTypes": ["ping", "status", "signalr_subscribe", "forward_to_target"]
}
```
**解决方案**: 使用支持的消息类型

## 🐛 调试功能

### 详细的调试日志

代理服务器提供了详细的调试日志，帮助你跟踪消息转发的整个过程：

#### 1. 客户端转发请求日志
```
📨 客户端 #1 请求转发消息:
   消息类型: object
   JSON 数据: {
     "command": "getData",
     "params": {
       "id": 123
     }
   }
   目标连接: #1
```

#### 2. 转发到目标服务器日志
```
🔄 转发消息到目标服务器:
   目标连接: #1 (Echo Server)
   目标类型: websocket
   数据类型: JSON
   数据大小: 45 字节
   二进制模式: 否
   JSON 数据内容:
{
  "command": "getData",
  "params": {
    "id": 123
  }
}
✅ 消息已成功转发到目标服务器
```

#### 3. 目标服务器响应日志
```
📥 目标连接 #1 (Echo Server) 收到消息:
   数据大小: 45 字节
   二进制模式: 否
   JSON 消息内容:
{
  "command": "getData",
  "params": {
    "id": 123
  }
}
📤 广播消息给 1 个客户端
```

### 启用调试模式

调试日志默认启用，你可以通过以下方式查看：

1. **服务器控制台** - 直接查看服务器输出
2. **测试脚本** - 运行 `npm run test:debug`
3. **Web 界面** - 在浏览器开发者工具中查看网络请求

## 💡 最佳实践

### 1. 消息分类
- 使用心跳消息维护连接活跃状态
- 使用状态查询获取服务器信息
- 使用转发消息与目标服务器交互

### 2. 错误处理
- 始终检查转发响应的 `success` 字段
- 处理连接断开和重连场景
- 实现消息重试机制

### 3. 性能优化
- 避免频繁发送大量转发消息
- 合理使用心跳间隔
- 监控目标服务器响应时间

### 4. 调试和监控
- 查看服务器控制台的详细日志
- 使用调试测试脚本验证功能
- 监控消息转发的成功率和延迟

### 5. 安全考虑
- 验证转发的消息内容
- 避免转发敏感信息
- 使用 API 密钥保护管理功能

## 🔗 相关文档

- [多目标连接指南](MULTI_TARGET_GUIDE.md)
- [测试客户端指南](TEST_CLIENT_GUIDE.md)
- [安全部署指南](SECURITY_GUIDE.md)

通过消息转发功能，你可以实现客户端与目标服务器之间的双向通信，同时保持代理服务器的控制和监控能力！
