const WebSocket = require('ws');

// 配置
const PROXY_URL = 'ws://localhost:8080';

console.log('🏎️  F1 实时数据演示\n');
console.log('此演示脚本连接到 F1 SignalR 代理服务器并显示实时数据');
console.log('请确保代理服务器已启用 SignalR 模式\n');

let messageCount = 0;
let dataTypes = new Set();

function parseF1Data(data) {
  try {
    const parsed = JSON.parse(data);
    
    // 检查是否是系统消息
    if (parsed.type === 'system') {
      console.log(`🔔 系统: ${parsed.message}`);
      return;
    }
    
    // 检查是否是 SignalR 数据
    if (parsed.M && Array.isArray(parsed.M)) {
      parsed.M.forEach(message => {
        if (message.M && message.A && Array.isArray(message.A)) {
          const method = message.M;
          const args = message.A;
          
          dataTypes.add(method);
          messageCount++;
          
          console.log(`📊 [${messageCount}] ${method}:`);
          
          // 根据不同的数据类型进行特殊处理
          switch (method) {
            case 'Heartbeat':
              console.log('   💓 心跳信号');
              break;
              
            case 'CarData.z':
              if (args[0]) {
                console.log(`   🚗 车辆数据: ${Object.keys(args[0]).length} 辆车`);
                // 显示前几辆车的数据
                Object.entries(args[0]).slice(0, 3).forEach(([carNum, data]) => {
                  console.log(`      车号 ${carNum}: 速度=${data.Speed || 'N/A'} 档位=${data.Gear || 'N/A'}`);
                });
              }
              break;
              
            case 'Position.z':
              if (args[0]) {
                console.log(`   📍 位置数据: ${Object.keys(args[0]).length} 辆车`);
                Object.entries(args[0]).slice(0, 3).forEach(([carNum, data]) => {
                  console.log(`      车号 ${carNum}: X=${data.X || 'N/A'} Y=${data.Y || 'N/A'} Z=${data.Z || 'N/A'}`);
                });
              }
              break;
              
            case 'TimingData':
              if (args[0] && args[0].Lines) {
                console.log(`   ⏱️  计时数据: ${Object.keys(args[0].Lines).length} 辆车`);
                Object.entries(args[0].Lines).slice(0, 3).forEach(([carNum, data]) => {
                  console.log(`      车号 ${carNum}: 最佳圈速=${data.BestLapTime?.Value || 'N/A'}`);
                });
              }
              break;
              
            case 'WeatherData':
              if (args[0]) {
                console.log(`   🌤️  天气数据: 温度=${args[0].AirTemp || 'N/A'}°C 湿度=${args[0].Humidity || 'N/A'}%`);
              }
              break;
              
            case 'TrackStatus':
              if (args[0]) {
                console.log(`   🏁 赛道状态: ${args[0].Status || 'N/A'} 消息=${args[0].Message || 'N/A'}`);
              }
              break;
              
            case 'SessionInfo':
              if (args[0]) {
                console.log(`   📋 赛段信息: 类型=${args[0].Type || 'N/A'} 名称=${args[0].Name || 'N/A'}`);
              }
              break;
              
            case 'RaceControlMessages':
              if (args[0]) {
                console.log(`   📢 赛事控制消息: ${args[0].Messages?.length || 0} 条消息`);
                if (args[0].Messages && args[0].Messages.length > 0) {
                  const latest = args[0].Messages[args[0].Messages.length - 1];
                  console.log(`      最新: ${latest.Message || 'N/A'}`);
                }
              }
              break;
              
            default:
              console.log(`   📦 数据长度: ${JSON.stringify(args).length} 字符`);
          }
        }
      });
    } else {
      // 其他格式的数据
      console.log(`📥 原始数据: ${data.substring(0, 100)}...`);
    }
    
  } catch (e) {
    console.log(`📥 非JSON数据: ${data.substring(0, 100)}...`);
  }
}

async function connectToF1Data() {
  console.log(`连接到 F1 代理服务器: ${PROXY_URL}\n`);
  
  return new Promise((resolve, reject) => {
    const ws = new WebSocket(PROXY_URL);
    let startTime;
    
    ws.on('open', () => {
      console.log('✅ 已连接到 F1 代理服务器');
      console.log('等待 F1 实时数据...\n');
      startTime = Date.now();
    });
    
    ws.on('message', (data) => {
      parseF1Data(data.toString());
    });
    
    ws.on('close', (code, reason) => {
      const duration = Date.now() - startTime;
      console.log(`\n❌ 连接已关闭 (${code}: ${reason || '无'})`);
      console.log(`\n📊 统计信息:`);
      console.log(`   - 连接持续时间: ${Math.round(duration / 1000)}秒`);
      console.log(`   - 接收消息数: ${messageCount}`);
      console.log(`   - 数据类型数: ${dataTypes.size}`);
      console.log(`   - 数据类型: ${Array.from(dataTypes).join(', ')}`);
      resolve();
    });
    
    ws.on('error', (error) => {
      console.error(`💥 WebSocket 错误: ${error.message}`);
      reject(error);
    });
    
    // 60秒后自动关闭
    setTimeout(() => {
      console.log('\n⏰ 演示时间到，关闭连接...');
      ws.close();
    }, 60000);
  });
}

async function checkF1ProxyStatus() {
  console.log('🔍 检查 F1 代理服务器状态...\n');
  
  try {
    const response = await fetch('http://localhost:8080/');
    const info = await response.json();
    
    console.log('📋 代理服务器信息:');
    console.log(`   - 模式: ${info.mode || 'WebSocket'}`);
    
    if (info.mode === 'SignalR') {
      console.log(`   - SignalR URL: ${info.signalrUrl}`);
      console.log(`   - Hub: ${info.signalrHub}`);
      console.log(`   - 连接状态: ${info.targetConnection}`);
      console.log(`   - 客户端数: ${info.clientConnections}`);
    } else {
      console.log('\n⚠️  警告: 代理服务器未启用 SignalR 模式');
      console.log('请使用以下命令启动 F1 模式:');
      console.log('npm run start:signalr');
      return false;
    }
    
    if (info.targetConnection !== 'connected') {
      console.log('\n⚠️  警告: 目标服务器未连接');
      console.log('请等待连接建立或检查网络连接');
      return false;
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ 无法连接到代理服务器:', error.message);
    console.log('请确保代理服务器正在运行在 http://localhost:8080');
    return false;
  }
}

async function main() {
  try {
    console.log('🎯 F1 实时数据演示程序\n');
    
    // 检查代理服务器状态
    const isReady = await checkF1ProxyStatus();
    if (!isReady) {
      process.exit(1);
    }
    
    console.log('\n✅ F1 代理服务器状态正常\n');
    
    // 等待用户确认
    console.log('按 Enter 键开始接收 F1 实时数据...');
    process.stdin.setRawMode(true);
    process.stdin.resume();
    await new Promise(resolve => {
      process.stdin.once('data', () => {
        process.stdin.setRawMode(false);
        resolve();
      });
    });
    
    // 连接并接收 F1 数据
    await connectToF1Data();
    
    console.log('\n🏁 F1 数据演示完成！');
    process.exit(0);
    
  } catch (error) {
    console.error('\n💥 演示失败:', error.message);
    process.exit(1);
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出程序...');
  process.exit(0);
});

// 运行演示
if (require.main === module) {
  main();
}
