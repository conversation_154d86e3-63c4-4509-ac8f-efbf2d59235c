# WebSocket 转发代理服务器 (一对多模式)

一个基于 Node.js 的 WebSocket 转发代理服务器，采用**一对多**架构模式。代理服务器与目标 WebSocket 服务器保持单一连接，然后将消息广播给所有连接的客户端。

## 🏗️ 架构特点

- **一对多模式**: 代理服务器维护与目标服务器的单一连接
- **单向广播**: 来自目标服务器的消息会广播给所有客户端
- **客户端消息不转发**: 客户端发送的消息不会转发到目标服务器，实现纯接收模式
- **资源节省**: 避免为每个客户端创建独立的目标连接
- **自动重连**: 目标连接断开时自动重连，支持指数退避策略
- **SignalR 支持**: 支持 SignalR 协议，包括自动协商和连接令牌管理

## 功能特性

- ✅ 一对多 WebSocket 广播架构
- ✅ 支持二进制和文本消息接收
- ✅ 客户端消息不转发（纯接收模式）
- ✅ **动态连接管理** - 通过 API 管理目标服务器连接
- ✅ SignalR 协议支持（自动协商、连接令牌管理）
- ✅ 自动重连机制
- ✅ 消息广播分发
- ✅ 连接状态监控
- ✅ REST API 接口
- ✅ 详细的日志记录
- ✅ 优雅关闭处理
- ✅ 健康检查端点

## 安装

1. 确保已安装 Node.js (版本 14 或更高)
2. 克隆或下载项目
3. 安装依赖：

```bash
npm install
```

## 使用方法

### 启动服务器

```bash
# 使用默认配置启动（自动连接模式）
npm start

# 启动但不自动连接（通过 API 管理连接）
npm run start:no-auto

# 启用自动转发模式（所有消息自动转发到目标服务器）
npm run start:auto-forward

# 启动 SignalR 模式（F1 实时数据示例）
npm run start:signalr

# 或者使用开发模式（自动重启）
npm run dev
```

### 环境变量配置

```bash
# 设置服务器端口（默认: 8080）
export PORT=3000

# 设置目标 WebSocket URL（默认: ws://echo.websocket.org）
export TARGET_WS_URL=ws://your-target-server.com

# 禁用日志（默认: 启用）
export ENABLE_LOGGING=false

# 设置重连间隔（默认: 5000ms）
export RECONNECT_INTERVAL=3000

# 设置最大重连次数（默认: 10）
export MAX_RECONNECT_ATTEMPTS=5

# SignalR 模式配置
export SIGNALR_MODE=true
export SIGNALR_URL=livetiming.formula1.com/signalr
export SIGNALR_HUB=Streaming
export SIGNALR_PROTOCOL=1.5
export USER_AGENT=BestHTTP
```

### 连接到代理服务器

在一对多模式下，所有客户端连接到同一个代理地址：

```javascript
const ws = new WebSocket('ws://localhost:8080');

// 监听消息（包括系统消息和目标服务器消息）
ws.onmessage = function(event) {
  try {
    const parsed = JSON.parse(event.data);
    if (parsed.type === 'system') {
      console.log('系统消息:', parsed.message);
    } else {
      console.log('数据消息:', event.data);
    }
  } catch (e) {
    console.log('数据消息:', event.data);
  }
};
```

## API 接口

### 连接管理 API

#### POST /api/connect
动态连接到目标服务器

**WebSocket 连接:**
```bash
curl -X POST http://localhost:8080/api/connect \
  -H "Content-Type: application/json" \
  -d '{"url": "ws://echo.websocket.org"}'
```

**SignalR 连接:**
```bash
curl -X POST http://localhost:8080/api/connect \
  -H "Content-Type: application/json" \
  -d '{
    "signalr": {
      "url": "livetiming.formula1.com/signalr",
      "hub": "Streaming",
      "protocol": "1.5"
    }
  }'
```

#### POST /api/disconnect
断开目标服务器连接

#### GET /api/config
获取当前配置

#### PUT /api/config
更新配置（如自动重连设置）

### 信息查询 API

#### GET /
获取服务器基本信息

```json
{
  "name": "WebSocket 转发代理 (一对多模式) - API 管理版",
  "version": "1.0.0",
  "status": "running",
  "clientConnections": 3,
  "targetConnection": "connected",
  "mode": "SignalR",
  "signalrUrl": "livetiming.formula1.com/signalr",
  "uptime": 3600,
  "apiEndpoints": ["POST /api/connect", "POST /api/disconnect", "GET /api/config", "PUT /api/config"]
}
```

### GET /status
获取详细的连接状态

```json
{
  "totalClients": 3,
  "clients": [
    {
      "id": 1,
      "ip": "::1",
      "uptime": 30000,
      "messagesSent": 5,
      "messagesReceived": 8,
      "status": "connected"
    }
  ],
  "targetConnection": {
    "url": "ws://echo.websocket.org",
    "status": "connected",
    "reconnectAttempts": 0
  },
  "serverUptime": 3600
}
```

### GET /health
健康检查端点

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### POST /signalr/subscribe
手动触发 SignalR 订阅（仅在 SignalR 模式下可用）

```json
{
  "success": true,
  "message": "SignalR 订阅消息已发送",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 使用场景

### WebSocket 模式
1. **实时数据广播**: 将单一数据源广播给多个客户端（如股票价格、实时新闻等）
2. **资源节省**: 避免为每个客户端创建独立的目标连接，节省服务器资源
3. **数据监控**: 多个监控客户端共享同一个数据源，只接收不发送
4. **实时仪表板**: 多个仪表板共享同一个数据流
5. **开发调试**: 在本地开发时多个开发者共享同一个测试数据流
6. **API 限制绕过**: 当目标服务器有连接数限制时，通过代理实现多客户端访问

### SignalR 模式
1. **F1 实时数据广播**: 将 Formula 1 实时比赛数据广播给多个客户端
2. **SignalR 服务代理**: 为不支持 SignalR 的客户端提供简化的 WebSocket 接口
3. **企业应用**: 广播企业内部 SignalR 服务的数据给多个客户端
4. **实时仪表板**: 多个仪表板共享同一个 SignalR 数据源
5. **移动应用**: 为移动应用提供简化的 WebSocket 接口接收 SignalR 数据
6. **数据分发**: 单一 SignalR 连接分发给多个不同类型的客户端应用

### API 管理模式
1. **多环境切换**: 开发、测试、生产环境间快速切换数据源
2. **故障转移**: 主服务器故障时快速切换到备用服务器
3. **A/B 测试**: 在不同数据源间进行对比测试
4. **运维管理**: 无停机更换后端数据源
5. **动态配置**: 根据业务需求动态调整连接参数

## 示例

### WebSocket 模式基本使用
```javascript
// 客户端代码
const ws = new WebSocket('ws://localhost:8080');

ws.onopen = () => {
  console.log('连接已建立');
  // 注意：发送的消息不会转发到目标服务器，仅用于测试
  ws.send('Hello, World!');
};

ws.onmessage = (event) => {
  // 处理系统消息和广播数据
  try {
    const parsed = JSON.parse(event.data);
    if (parsed.type === 'system') {
      console.log('系统消息:', parsed.message);
      return;
    }
  } catch (e) {
    // 不是系统消息，按广播数据处理
  }
  console.log('收到广播数据:', event.data);
};

ws.onclose = () => {
  console.log('连接已关闭');
};
```

### 多客户端广播测试
```javascript
// 创建多个客户端连接
const clients = [];
for (let i = 0; i < 3; i++) {
  const ws = new WebSocket('ws://localhost:8080');
  ws.onmessage = (event) => {
    console.log(`客户端 ${i} 收到广播:`, event.data);
  };
  clients.push(ws);
}

// 注意：客户端发送的消息不会转发到目标服务器
// 所有客户端只会收到来自目标服务器的广播数据
setTimeout(() => {
  clients[0].send('这条消息不会转发到目标服务器');
}, 1000);
```

### SignalR 模式使用

```bash
# 启动 SignalR 模式服务器
SIGNALR_MODE=true SIGNALR_URL=livetiming.formula1.com/signalr SIGNALR_HUB=Streaming npm start
```

```javascript
// SignalR 客户端连接
const ws = new WebSocket('ws://localhost:8080');

ws.onopen = () => {
  console.log('连接已建立');

  // 服务器会自动发送默认订阅消息
  // 客户端发送的订阅消息不会转发，订阅由服务器统一管理
  console.log('服务器已自动订阅所有 F1 数据流');
};

ws.onmessage = (event) => {
  try {
    const parsed = JSON.parse(event.data);

    // 处理系统消息
    if (parsed.type === 'system') {
      console.log('系统消息:', parsed.message);
      return;
    }

    // 处理 SignalR 数据
    if (parsed.M) {
      console.log('SignalR 数据:', parsed.M);
    }
  } catch (e) {
    console.log('原始数据:', event.data);
  }
};
```

### 测试连接

```bash
# 命令行测试
npm run test:forward      # 测试消息转发功能
npm run test:json         # 测试 JSON 数据转发
npm run test:pure         # 测试纯净数据转发
npm run test:field        # 测试基于消息字段的转发控制
npm run test:signalr      # 测试 SignalR 模式
npm run test:api          # 测试 API 管理功能
npm run test:multi        # 测试多目标功能

# Web 界面测试
npm run test:web          # 提示打开 Web 测试客户端
npm run test:web-api      # 测试 Web API 自动转发功能
npm run test:web-interface # 测试 Web 界面访问
```

### 消息转发功能

项目支持两种消息处理模式：

#### 1. 心跳和控制消息（不转发）
用于客户端与代理服务器之间的通信，不会转发到目标服务器：

```javascript
// 心跳消息
ws.send(JSON.stringify({ type: "ping" }));

// 状态查询
ws.send(JSON.stringify({ type: "status" }));

// SignalR 订阅
ws.send(JSON.stringify({ type: "signalr_subscribe" }));
```

#### 2. 基于消息字段的转发控制（推荐）
通过在消息中添加 `forward` 字段来精确控制每个消息是否转发：

```javascript
// 转发消息（使用 data 字段）
ws.send(JSON.stringify({
  forward: true,
  data: { command: "getData", params: { id: 123 } }
}));

// 转发消息（直接内容）
ws.send(JSON.stringify({
  forward: true,
  command: "processData",
  value: 456
}));

// 不转发消息
ws.send(JSON.stringify({
  forward: false,
  data: "This will not be forwarded"
}));
```

#### 3. 传统转发方式
```javascript
// 手动转发
ws.send(JSON.stringify({
  type: "forward_to_target",
  data: "Hello Target Server!"
}));
```

### Web 测试客户端

项目包含一个功能强大的 Web 测试界面，提供：

- **WebSocket 连接测试** - 图形化界面测试连接
- **目标连接管理** - 创建、查看、删除目标连接
- **自动转发模式切换** - 动态启用/禁用自动转发
- **JSON 数据转发** - 支持复杂 JSON 数据转发
- **API 调试工具** - 测试所有 REST API 端点
- **实时监控** - 监控服务器状态和连接数据

**推荐使用方法：**
1. 启动代理服务器：`npm run start:no-auto`
2. 在浏览器中访问：`http://localhost:8080`
3. 开始测试各种功能

**注意：** 请通过 HTTP 服务器访问，不要直接打开 HTML 文件。

详细使用说明请参阅 [测试客户端指南](TEST_CLIENT_GUIDE.md)。

### API 管理示例

```bash
# 连接到 WebSocket 服务器
curl -X POST http://localhost:8080/api/connect \
  -d '{"url": "ws://echo.websocket.org"}'

# 切换到 SignalR 服务器
curl -X POST http://localhost:8080/api/connect \
  -d '{
    "signalr": {
      "url": "livetiming.formula1.com/signalr",
      "hub": "Streaming",
      "protocol": "1.5"
    }
  }'

# 手动触发 SignalR 订阅
curl -X POST http://localhost:8080/signalr/subscribe

# 断开连接
curl -X POST http://localhost:8080/api/disconnect
```

### 使用 curl 测试 API
```bash
# 获取服务器状态
curl http://localhost:8080/status

# 健康检查
curl http://localhost:8080/health
```

## 注意事项

- **单一目标连接**: 代理服务器只维护一个到目标服务器的连接
- **单向广播**: 来自目标服务器的所有消息都会广播给所有客户端
- **客户端消息不转发**: 客户端发送的消息不会转发到目标服务器，实现纯接收模式
- **自动重连**: 目标连接断开时会自动重连，支持指数退避策略
- **系统消息**: 代理服务器会发送 JSON 格式的系统消息通知客户端状态变化
- **资源共享**: 多个客户端共享同一个目标连接，所有客户端接收相同的广播数据
- **连接管理**: 即使所有客户端断开，代理服务器也会保持目标连接以便快速响应新连接

### SignalR 特殊注意事项

- **自动订阅**: SignalR 模式下，服务器会在连接建立后自动发送 F1 数据订阅消息
- **初始化消息缓存**: 系统会自动缓存第一条以 `{"R":{"Heartbeat":` 开头的初始化消息，并发送给每个新连接的客户端
- **订阅统一管理**: 订阅由代理服务器统一管理，客户端无需发送订阅消息
- **协商过程**: 服务器会自动处理 SignalR 协商过程，客户端无需关心连接令牌等细节
- **消息格式**: SignalR 消息通常是特定格式的 JSON，包含 H（Hub）、M（Method）、A（Arguments）等字段
- **纯接收模式**: 客户端只接收广播数据，不能向 SignalR 服务器发送数据

## 许可证

MIT License
