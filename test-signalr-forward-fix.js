const WebSocket = require('ws');

console.log('🔧 测试 SignalR 模式下的转发修复\n');

// 简单的 HTTP 请求函数
function httpRequest(url, method = 'GET', data = null) {
  const http = require('http');
  const urlParts = new URL(url);
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: urlParts.hostname,
      port: urlParts.port,
      path: urlParts.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function checkServerMode() {
  console.log('🔍 检查服务器模式...');
  
  try {
    const response = await httpRequest('http://localhost:8080/api/config');
    
    if (response.status === 200) {
      console.log(`✅ 服务器配置获取成功`);
      console.log(`   模式: ${response.data.mode}`);
      console.log(`   SignalR 模式: ${response.data.signalrMode ? '启用' : '禁用'}`);
      console.log(`   自动连接: ${response.data.autoConnect ? '启用' : '禁用'}`);
      
      return response.data.signalrMode;
    } else {
      console.log(`❌ 无法获取服务器配置，状态码: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log('❌ 检查服务器模式时出错:', error.message);
    return false;
  }
}

async function createTestTarget() {
  console.log('\n🔗 创建测试目标连接...');
  
  try {
    const response = await httpRequest('http://localhost:8080/api/targets', 'POST', {
      name: 'SignalR Forward Fix Test',
      url: 'ws://echo.websocket.org'
    });
    
    if (response.status === 200 && response.data.success) {
      console.log(`✅ 目标连接创建成功，ID: ${response.data.connection.id}`);
      return response.data.connection.id;
    } else {
      console.log('❌ 目标连接创建失败:', response.data.error || response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ 创建目标连接时出错:', error.message);
    return null;
  }
}

async function testSignalRForwardFix(targetId, isSignalRMode) {
  console.log(`\n🧪 测试 ${isSignalRMode ? 'SignalR' : '普通'} 模式下的转发功能...\n`);
  
  return new Promise((resolve) => {
    const wsUrl = `ws://localhost:8080?target=${targetId}`;
    const ws = new WebSocket(wsUrl);
    
    let testResults = {
      connected: false,
      forwardMessageReceived: false,
      echoReceived: false
    };
    
    const timeout = setTimeout(() => {
      console.log('⏰ 测试超时');
      ws.close();
      resolve(testResults);
    }, 15000);
    
    ws.on('open', () => {
      console.log('✅ WebSocket 连接已建立');
      testResults.connected = true;
      
      // 发送包含 forward: true 的测试消息
      const testMessage = {
        forward: true,
        data: ["6", "6", "driver_radio_transcriptions:session:9921", "phx_join", {}]
      };
      
      console.log('📤 发送测试消息:');
      console.log(`   ${JSON.stringify(testMessage)}`);
      
      setTimeout(() => {
        ws.send(JSON.stringify(testMessage));
      }, 1000);
      
      // 关闭连接
      setTimeout(() => {
        ws.close();
      }, 8000);
    });
    
    ws.on('message', (data) => {
      const message = data.toString();
      console.log(`📥 收到响应: ${message.substring(0, 150)}...`);
      
      try {
        const parsed = JSON.parse(message);
        
        switch (parsed.type) {
          case 'system':
            console.log(`   [系统] ${parsed.message}`);
            if (parsed.reason) {
              console.log(`   原因: ${parsed.reason}`);
            }
            break;
            
          case 'auto_forward_response':
            console.log(`   [转发响应] 成功: ${parsed.success}`);
            console.log(`   消息: ${parsed.message}`);
            if (parsed.success) {
              testResults.forwardMessageReceived = true;
              console.log(`   ✅ 转发功能在 ${isSignalRMode ? 'SignalR' : '普通'} 模式下正常工作！`);
            }
            break;
            
          default:
            console.log(`   [其他] ${parsed.type}: ${parsed.message || 'N/A'}`);
        }
      } catch (e) {
        // 可能是 Echo 服务器的回显
        if (message.includes('phx_join')) {
          console.log(`   📥 收到 Echo 服务器回显 - 转发成功！`);
          testResults.echoReceived = true;
        } else {
          console.log(`   📥 收到原始回显: ${message.substring(0, 100)}...`);
        }
      }
    });
    
    ws.on('close', () => {
      clearTimeout(timeout);
      console.log('\n❌ WebSocket 连接已关闭');
      resolve(testResults);
    });
    
    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.error('💥 WebSocket 错误:', error.message);
      resolve(testResults);
    });
  });
}

async function cleanupTarget(targetId) {
  console.log(`\n🧹 清理测试目标连接 #${targetId}...`);
  
  try {
    const response = await httpRequest(`http://localhost:8080/api/targets/${targetId}`, 'DELETE');
    
    if (response.status === 200) {
      console.log('✅ 目标连接已删除');
    } else {
      console.log('⚠️  删除目标连接时出现问题');
    }
  } catch (error) {
    console.log('❌ 删除目标连接时出错:', error.message);
  }
}

async function main() {
  console.log('🎯 SignalR 模式转发修复验证工具\n');
  console.log('📋 此工具将：');
  console.log('   - 检查服务器是否运行在 SignalR 模式');
  console.log('   - 测试 forward: true 消息是否正确转发');
  console.log('   - 验证修复是否生效\n');
  
  try {
    // 1. 检查服务器模式
    const isSignalRMode = await checkServerMode();
    
    // 2. 创建测试目标
    const targetId = await createTestTarget();
    if (!targetId) {
      console.log('❌ 无法创建测试目标，退出测试');
      return;
    }
    
    // 3. 等待目标连接建立
    console.log('⏳ 等待目标连接建立...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 4. 测试转发功能
    const results = await testSignalRForwardFix(targetId, isSignalRMode);
    
    // 5. 分析测试结果
    console.log('\n📊 测试结果分析:');
    console.log(`   连接建立: ${results.connected ? '✅' : '❌'}`);
    console.log(`   转发响应: ${results.forwardMessageReceived ? '✅' : '❌'}`);
    console.log(`   Echo 回显: ${results.echoReceived ? '✅' : '❌'}`);
    
    if (results.connected && (results.forwardMessageReceived || results.echoReceived)) {
      console.log(`\n🎉 修复成功！转发功能在 ${isSignalRMode ? 'SignalR' : '普通'} 模式下正常工作`);
      console.log('   - forward: true 消息正确识别和转发');
      console.log('   - 消息成功到达目标服务器');
    } else {
      console.log('\n⚠️  修复可能不完整，请检查服务器日志');
    }
    
    // 6. 清理测试目标
    await cleanupTarget(targetId);
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 收到中断信号，退出测试...');
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  console.log('请确保代理服务器正在运行\n');
  main();
}
