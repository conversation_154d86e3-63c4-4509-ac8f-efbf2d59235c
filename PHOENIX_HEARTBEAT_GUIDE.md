# Phoenix Channel 心跳功能指南

## 🫀 功能概述

当代理服务器检测到来自目标服务器的 Phoenix Channel `phx_reply` 消息且状态为 `ok` 时，**代理服务器会自动启动内部心跳机制**，每秒直接向目标服务器发送心跳消息以保持连接活跃。

**重要说明**: 这是代理服务器与目标服务器之间的内部行为，与客户端无关。客户端不参与心跳过程。

## 🔥 触发条件

### 检测消息格式
```json
["6", "6", "driver_radio_transcriptions:session:9921", "phx_reply", {"status": "ok", "response": {}}]
```

### 触发条件
1. **消息是数组格式** - 包含至少 4 个元素
2. **第 4 个元素是 "phx_reply"** - Phoenix Channel 回复事件
3. **第 5 个元素包含 status: "ok"** - 状态为成功

## 💓 心跳消息格式

### 心跳数据结构
```json
[null, "7", "phoenix", "heartbeat", {}]
```

### 字段说明
- **第 1 个元素**: `null` - 引用字段
- **第 2 个元素**: `"7"` - 计数器，从 7 开始，每秒递增 1
- **第 3 个元素**: `"phoenix"` - Phoenix Channel 标识
- **第 4 个元素**: `"heartbeat"` - 心跳事件类型
- **第 5 个元素**: `{}` - 空的负载对象

### 计数器递增示例
```json
[null, "7", "phoenix", "heartbeat", {}]   // 第 1 秒
[null, "8", "phoenix", "heartbeat", {}]   // 第 2 秒
[null, "9", "phoenix", "heartbeat", {}]   // 第 3 秒
[null, "10", "phoenix", "heartbeat", {}]  // 第 4 秒
// ... 持续递增
```

## 🚀 使用流程

### 1. 建立连接
```javascript
// 创建目标连接到 Phoenix Channel 服务器
const targetResponse = await fetch('http://localhost:8080/api/targets', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'Phoenix Channel Server',
    url: 'ws://your-phoenix-server.com/socket/websocket'
  })
});

const targetId = targetResponse.data.connection.id;
```

### 2. 连接到代理服务器
```javascript
const ws = new WebSocket(`ws://localhost:8080?target=${targetId}`);
```

### 3. 发送 phx_join 消息
```javascript
ws.onopen = () => {
  // 发送 Phoenix Channel 加入消息
  const joinMessage = {
    forward: true,
    data: ["6", "6", "driver_radio_transcriptions:session:9921", "phx_join", {}]
  };
  
  ws.send(JSON.stringify(joinMessage));
};
```

### 4. 自动心跳启动（代理服务器内部行为）
当目标服务器响应 `phx_reply` 且状态为 `ok` 时，**代理服务器内部**会：

1. **检测响应消息**
   ```
   🔥 检测到 Phoenix Channel phx_reply 状态为 ok，启动心跳机制
   ```

2. **启动内部心跳定时器**
   ```
   🫀 启动 Phoenix Channel 心跳 - 目标连接 #1: Phoenix Channel Server
   ```

3. **代理服务器直接向目标服务器发送心跳**
   ```
   💓 代理服务器发送心跳 #7 到目标服务器: [null,"7","phoenix","heartbeat",{}]
   💓 代理服务器发送心跳 #8 到目标服务器: [null,"8","phoenix","heartbeat",{}]
   💓 代理服务器发送心跳 #9 到目标服务器: [null,"9","phoenix","heartbeat",{}]
   ```

**注意**: 客户端不会看到这些心跳消息，这是代理服务器的内部维护行为。

## 🔧 心跳管理

### 自动启动
- 检测到 `phx_reply` 状态 `ok` 时自动启动
- 无需手动配置或触发

### 自动停止
心跳会在以下情况自动停止：

1. **目标连接断开**
   ```
   💔 停止 Phoenix Channel 心跳 - 目标连接 Phoenix Channel Server
   ```

2. **手动删除目标连接**
   ```bash
   DELETE /api/targets/{id}
   ```

3. **服务器关闭**
   - 所有心跳定时器会被清理

### 错误处理
- 如果心跳发送失败，会自动停止心跳机制
- 如果目标连接状态异常，会停止心跳并记录日志

## 🧪 测试验证

### 自动化测试
```bash
# 测试 Phoenix Channel 心跳功能
npm run test:phoenix
```

### 手动测试
1. **启动代理服务器**
   ```bash
   npm run start:no-auto
   ```

2. **创建目标连接**
   ```bash
   curl -X POST http://localhost:8080/api/targets \
     -H "Content-Type: application/json" \
     -d '{"name": "Phoenix Test", "url": "ws://your-phoenix-server.com"}'
   ```

3. **连接并发送 phx_join**
   ```javascript
   const ws = new WebSocket('ws://localhost:8080?target=1');
   ws.onopen = () => {
     ws.send(JSON.stringify({
       forward: true,
       data: ["6", "6", "channel", "phx_join", {}]
     }));
   };
   ```

4. **观察服务器日志**
   - 查看心跳启动消息
   - 确认每秒发送心跳
   - 验证计数器递增

## 📊 监控和调试

### 服务器日志
启动心跳时：
```
🔥 检测到 Phoenix Channel phx_reply 状态为 ok，启动心跳机制
🫀 启动 Phoenix Channel 心跳 - 目标连接 #1: Phoenix Channel Server
```

发送心跳时：
```
💓 发送 Phoenix 心跳 #7 到目标 #1: [null,"7","phoenix","heartbeat",{}]
💓 发送 Phoenix 心跳 #8 到目标 #1: [null,"8","phoenix","heartbeat",{}]
```

停止心跳时：
```
💔 停止 Phoenix Channel 心跳 - 目标连接 Phoenix Channel Server
```

### 错误日志
发送失败：
```
❌ 发送 Phoenix 心跳失败: WebSocket is not open
```

连接异常：
```
⚠️ 目标连接 #1 已断开，停止心跳
```

## 🎯 使用场景

### Phoenix LiveView 应用
```javascript
// 连接到 Phoenix LiveView
const joinMessage = {
  forward: true,
  data: ["1", "1", "lv:page", "phx_join", {
    "url": "http://localhost:4000/",
    "params": {},
    "session": "session_token"
  }]
};
```

### Phoenix Channel 订阅
```javascript
// 订阅特定频道
const joinMessage = {
  forward: true,
  data: ["2", "2", "room:lobby", "phx_join", {}]
};
```

### F1 数据流订阅
```javascript
// 订阅 F1 实时数据
const joinMessage = {
  forward: true,
  data: ["6", "6", "driver_radio_transcriptions:session:9921", "phx_join", {}]
};
```

## 💡 最佳实践

### 1. 连接管理
- 确保目标服务器支持 Phoenix Channel 协议
- 监控心跳日志确认连接健康状态
- 在连接断开时检查心跳是否正确停止

### 2. 错误处理
- 监控服务器日志中的心跳错误
- 如果心跳频繁失败，检查目标服务器状态
- 必要时重新创建目标连接

### 3. 性能考虑
- 心跳每秒发送一次，对性能影响很小
- 多个目标连接可以同时运行独立的心跳
- 心跳会在连接断开时自动清理，无内存泄漏

## 🔍 故障排除

### 问题 1: 心跳未启动
**症状**: 发送 phx_join 后没有看到心跳日志
**检查**:
1. 确认收到了 phx_reply 消息
2. 检查 phx_reply 的状态是否为 "ok"
3. 查看服务器日志中的消息解析信息

### 问题 2: 心跳格式错误
**症状**: 目标服务器拒绝心跳消息
**检查**:
1. 验证心跳消息格式：`[null, "计数器", "phoenix", "heartbeat", {}]`
2. 确认计数器是字符串格式
3. 检查负载是否为空对象

### 问题 3: 心跳停止
**症状**: 心跳发送一段时间后停止
**检查**:
1. 检查目标连接状态
2. 查看是否有发送错误日志
3. 确认目标服务器是否仍然在线

通过 Phoenix Channel 心跳功能，代理服务器可以自动维护与 Phoenix 服务器的长连接，确保实时数据流的稳定性！🎉
